import { NextRequest, NextResponse } from 'next/server';
import { connectDB, SubIntegration } from '@/lib/mongodb';
import * as XLSX from 'xlsx';
import { format } from 'date-fns';

export async function GET(request: NextRequest) {
  try {
    await connectDB();
    
    // Get all sub-integrations with populated masterOtaId
    const subIntegrations = await SubIntegration.find()
      .populate('masterOtaId', 'otaName type country iata trueIata fop clientId userId contact')
      .sort({ startDate: -1 });
    
    // Prepare data for Excel
    const data = subIntegrations.map(integration => {
      const masterOta = integration.masterOtaId || {};
      const formatDate = (date: Date | null | undefined) => {
        if (!date) return 'N/A';
        try {
          return format(new Date(date), 'dd-MMM-yy');
        } catch {
          return 'N/A';
        }
      };
      
      return {
        'Batch': integration.batch || 'N/A',
        'OTA': masterOta.otaName || 'N/A',
        'Type': masterOta.type || 'N/A',
        'Country': masterOta.country || 'N/A',
        'IATA': masterOta.iata || 'N/A',
        'True IATA': masterOta.trueIata || 'N/A',
        'Form of Payment': masterOta.fop || 'N/A',
        'Client ID': masterOta.clientId || 'N/A',
        'User ID': masterOta.userId || 'N/A',
        'Contact': masterOta.contact || 'N/A',
        'Flow': integration.flow || 'N/A',
        'Start Date': formatDate(integration.startDate),
        'Progress': `${integration.progress || 0}%`,
        'Status': integration.status || 'N/A',
        'Expected End': formatDate(integration.expectedEndDate),
        'Actual End': formatDate(integration.actualEndDate),
        'Manager': integration.manager || 'N/A'
      };
    });
    
    // Create workbook and worksheet
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(data);
    
    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Sub-Integrations');
    
    // Generate Excel file
    const excelBuffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
    
    // Prepare response headers
    const headers = new Headers();
    headers.append('Content-Disposition', 'attachment; filename=subintegrations.xlsx');
    headers.append('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    
    return new NextResponse(excelBuffer, {
      status: 200,
      headers,
    });
  } catch (error) {
    console.error('Error exporting sub-integrations:', error);
    return NextResponse.json(
      { error: 'Failed to export sub-integrations' },
      { status: 500 }
    );
  }
} 