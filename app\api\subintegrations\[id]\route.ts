import { NextRequest, NextResponse } from 'next/server';
import { connectDB, SubIntegration } from '@/lib/mongodb';
import mongoose from 'mongoose';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB();
    
    if (!mongoose.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json(
        { error: 'Invalid ID format' },
        { status: 400 }
      );
    }
    
    const subIntegration = await SubIntegration.findById(params.id);
    
    if (!subIntegration) {
      return NextResponse.json(
        { error: 'Sub-integration not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(subIntegration);
  } catch (error) {
    console.error('Error fetching sub-integration:', error);
    return NextResponse.json(
      { error: 'Failed to fetch sub-integration' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const data = await request.json();
    await connectDB();
    
    if (!mongoose.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json(
        { error: 'Invalid ID format' },
        { status: 400 }
      );
    }
    
    // Fetch existing subintegration
    const existingSubIntegration = await SubIntegration.findById(params.id);
    if (!existingSubIntegration) {
      return NextResponse.json(
        { error: 'Sub-integration not found' },
        { status: 404 }
      );
    }

    // Calculate expected end date if start date or weeks changed
    const startDate = data.startDate ? new Date(data.startDate) : existingSubIntegration.startDate;
    const weeks = data.weeks || existingSubIntegration.weeks;
    const expectedEndDate = new Date(startDate);
    expectedEndDate.setDate(startDate.getDate() + (weeks * 7));
    
    // Calculate progress
    const currentDate = new Date();
    const timeDifference = currentDate.getTime() - startDate.getTime();
    const weeksElapsed = Math.max(0, Math.round((timeDifference / (1000 * 60 * 60 * 24 * 7)) * 10) / 10);
    const progress = Math.min(100, Math.round((weeksElapsed / weeks) * 100));
    
    const updateDoc = {
      batch: data.batch,
      ota: data.ota,
      masterOtaId: data.masterOtaId,
      flow: data.flow,
      startDate,
      weeks,
      progress,
      status: data.status,
      expectedEndDate,
      manager: data.manager,
      actualEndDate: data.actualEndDate ? new Date(data.actualEndDate) : existingSubIntegration.actualEndDate
    };
    
    const updatedSubIntegration = await SubIntegration.findByIdAndUpdate(
      params.id,
      { $set: updateDoc },
      { new: true }
    );
    
    if (!updatedSubIntegration) {
      return NextResponse.json(
        { error: 'Sub-integration not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(updatedSubIntegration);
  } catch (error) {
    console.error('Error updating sub-integration:', error);
    return NextResponse.json(
      { error: 'Failed to update sub-integration' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB();
    
    if (!mongoose.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json(
        { error: 'Invalid ID format' },
        { status: 400 }
      );
    }
    
    const deletedSubIntegration = await SubIntegration.findByIdAndDelete(params.id);
    
    if (!deletedSubIntegration) {
      return NextResponse.json(
        { error: 'Sub-integration not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting sub-integration:', error);
    return NextResponse.json(
      { error: 'Failed to delete sub-integration' },
      { status: 500 }
    );
  }
} 