"use client";

import * as React from "react";
import { Header } from "@/components/header";
import { UsageTable } from "@/components/usages/usage-table";
import { NewUsageDialog } from "@/components/usages/new-usage-dialog";
import { EditUsageDialog } from "@/components/usages/edit-usage-dialog";
import { MasterOTA } from "@/types";
import useSWR from "swr";
import { Download } from "lucide-react";
import { Button } from "@/components/ui/button";

const fetcher = (url: string) => fetch(url).then((res) => res.json());

export default function UsagesPage() {
  const { data: usages = [], error, isLoading } = useSWR<MasterOTA[]>("/api/usages", fetcher);
  const [editingUsage, setEditingUsage] = React.useState<MasterOTA | null>(null);

  const handleExport = async () => {
    try {
      const response = await fetch(`/api/usages?export=true`);
      if (!response.ok) throw new Error('Export failed');
      
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'integration-modules.xlsx';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error exporting integration modules:', error);
    }
  };

  if (error) {
    return (
      <div className="min-h-screen">
        <Header />
        <div className="px-4 py-6">
          <div className="text-red-500">Error loading modules</div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <Header />
      <div className="px-4 py-6">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-2xl font-bold">Integration modules</h1>
          <div className="flex gap-2">
            <Button
              onClick={handleExport}
              className="flex items-center gap-2"
              variant="outline"
              size="sm"
            >
              <Download className="h-4 w-4" />
              <span>Export</span>
            </Button>
            <NewUsageDialog />
          </div>
        </div>

        {isLoading ? (
          <div className="text-center py-4">Loading...</div>
        ) : (
          <UsageTable usages={usages} onEdit={setEditingUsage} />
        )}

        {editingUsage && (
          <EditUsageDialog
            usage={editingUsage}
            open={!!editingUsage}
            onOpenChange={(open) => !open && setEditingUsage(null)}
          />
        )}
      </div>
    </div>
  );
}
