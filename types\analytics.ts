/**
 * Analytics data types
 */

export interface AnalyticsData {
  Date: string;
  OTAName: string;
  Success: string;
  WrongRoutes: string;
  Throttled: string;
  Failures: string;
  Bookings: string;
  LookToBook: string;
  [key: string]: string | number;
}

export interface StatusSummary {
  totalCounts: {
    Success: number;
    WrongRoutes: number;
    Throttled: number;
    Failures: number;
    Bookings: number;
    AverageLookToBook: number;
  };
  percentages: {
    Success: number;
    WrongRoutes: number;
    Throttled: number;
    Failures: number;
    Conversion: number;
  };
  percentageChanges: {
    Success: number;
    WrongRoutes: number;
    Throttled: number;
    Failures: number;
  };
}

export interface DailyLook {
  date: string;
  Success: number;
  Failures: number;
  "Non-Operating Routes": number;
  Throttled: number;
  originalDate?: string;
  OTAName?: string;
}

export interface DailyBook {
  date: string;
  BookingCounts: number;
  originalDate?: string;
  OTAName?: string;
}

export interface ChartDataItem {
  date: string;
  looks: number;
  bookings: number;
  lookToBook: number;
  wrongRoutes: number;
  throttled: number;
  errors: number;
}

export interface TotalMetrics {
  Success: number;
  Failures: number;
  "Non-Operating Routes": number;
  Throttled: number;
}

export interface StatCardItem {
  id: number;
  title: string;
  dataKey: string;
  value: string;
  percentage: string;
  icon: React.ElementType;
  color: string;
  bgColor: string;
  lightBgColor: string;
  change: number;
  changeType: 'increase' | 'decrease';
}

export interface OTAData {
  OTAName: string;
  TotalLooks: number;
  SuccessLooks: number;
  TotalBookings: number;
  LookToBookRatio: number;
}
