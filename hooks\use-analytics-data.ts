import { useState, useEffect } from 'react';
import useSWR from 'swr';
import { parseDate } from '@/lib/date-utils';
import { AnalyticsData, StatusSummary, DailyLook, DailyBook } from '@/types/analytics';

const fetcher = (url: string) => fetch(url).then(res => res.json());

interface UseAnalyticsDataProps {
  currentPage: number;
  pageSize: number;
  searchTerm: string;
}

interface UseAnalyticsDataReturn {
  analyticsData: AnalyticsData[];
  columns: string[];
  statusSummary: StatusSummary;
  totalPages: number;
  isLoading: boolean;
  error: Error | null;
  dailyLooksData: DailyLook[];
  dailyBooksData: DailyBook[];
  hydrated: boolean;
}

/**
 * Custom hook for fetching and managing analytics data
 */
export function useAnalyticsData({
  currentPage,
  pageSize,
  searchTerm
}: UseAnalyticsDataProps): UseAnalyticsDataReturn {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData[]>([]);
  const [columns, setColumns] = useState<string[]>([]);
  const [hydrated, setHydrated] = useState(false);
  const [statusSummary, setStatusSummary] = useState<StatusSummary>({
    totalCounts: {
      Success: 0,
      WrongRoutes: 0,
      Throttled: 0,
      Failures: 0,
      Bookings: 0,
      AverageLookToBook: 0
    },
    percentages: {
      Success: 0,
      WrongRoutes: 0,
      Throttled: 0,
      Failures: 0,
      Conversion: 0
    },
    percentageChanges: {
      Success: 0,
      WrongRoutes: 0,
      Throttled: 0,
      Failures: 0
    }
  });
  const [totalPages, setTotalPages] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // Fetch daily looks and books data
  const { data: dailyLooks } = useSWR(
    `/api/analytics-mongo/daily-looks${searchTerm ? `?search=${encodeURIComponent(searchTerm)}` : ''}`,
    fetcher
  );
  
  const { data: dailyBooks } = useSWR(
    `/api/analytics-mongo/daily-books${searchTerm ? `?search=${encodeURIComponent(searchTerm)}` : ''}`,
    fetcher
  );

  // Process daily looks data
  const dailyLooksData: DailyLook[] = dailyLooks?.data?.length
    ? dailyLooks.data.map((entry: any) => {
        const dateObj = parseDate(entry.date);
        
        return {
          date: dateObj.toISOString(),
          Success: entry.Success,
          Failures: entry.Failures,
          "Non-Operating Routes": entry.WrongRoutes,
          Throttled: entry.Throttled,
          originalDate: entry.date
        };
      })
    : [];

  // Process daily books data
  const dailyBooksData: DailyBook[] = dailyBooks?.data?.length
    ? dailyBooks.data.map((entry: any) => {
        const dateObj = parseDate(entry.date);
        
        return {
          date: dateObj.toISOString(),
          BookingCounts: entry.BookingCounts,
          originalDate: entry.date
        };
      })
    : [];

  // Fetch main analytics data
  const fetchData = async (query = "") => {
    try {
      setIsLoading(true);
      const response = await fetch(
        `/api/analytics-mongo?page=${currentPage}&pageSize=${pageSize}${query}`
      );
      
      if (!response.ok) {
        throw new Error(`Error fetching analytics data: ${response.statusText}`);
      }
      
      const { data, columns, totalCounts, percentages, percentageChanges, totalPages } = await response.json();

      setAnalyticsData(data || []);
      setColumns(columns.includes("Bookings") ? columns : [...columns, "Bookings"]);
      setStatusSummary({
        totalCounts: {
          ...totalCounts,
          Bookings: totalCounts?.Bookings || 0,
          AverageLookToBook: totalCounts?.AverageLookToBook || 0
        },
        percentages: {
          ...percentages,
          Conversion: percentages?.Conversion || 0
        },
        percentageChanges
      });
      setTotalPages(totalPages || 1);
      setHydrated(true);
      setError(null);
    } catch (error) {
      console.error("Error fetching analytics data:", error);
      setError(error instanceof Error ? error : new Error('Unknown error occurred'));
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch data when dependencies change
  useEffect(() => {
    const query = searchTerm ? `&search=${encodeURIComponent(searchTerm)}` : "";
    fetchData(query);
  }, [currentPage, pageSize, searchTerm]);

  return {
    analyticsData,
    columns,
    statusSummary,
    totalPages,
    isLoading,
    error,
    dailyLooksData,
    dailyBooksData,
    hydrated
  };
}
