'use client';

import { <PERSON><PERSON> } from "@/components/header";
import ConversionMetrics from "@/components/analytics/conversion-metrics";
import AnalyticsTable from "@/components/analytics/analytics-table";
import { Download } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { AnalyticsSkeleton } from "@/components/analytics/analytics-skeleton";
import { SearchBar } from "@/components/analytics/search-bar";
import { useAnalyticsData } from "@/hooks/use-analytics-data";
import { usePagination } from "@/hooks/use-pagination";
import { useSearch } from "@/hooks/use-search";
import { getCurrentDateISO } from "@/lib/date-utils";

export default function AnalyticsPage() {
  // Set up pagination
  const {
    currentPage,
    pageSize,
    totalPages,
    setTotalPages,
    handlePageChange
  } = usePagination();

  // Set up search
  const {
    searchTerm,
    searchInput,
    setSearchInput,
    handleSearch,
    handleKeyPress,
    isSearching
  } = useSearch({
    onSearch: () => handlePageChange(1)
  });

  // Set up analytics data
  const {
    analyticsData,
    columns,
    totalPages: apiTotalPages,
    hydrated
  } = useAnalyticsData({
    currentPage,
    pageSize,
    searchTerm
  });

  // Update total pages when API returns new value
  if (apiTotalPages !== totalPages) {
    setTotalPages(apiTotalPages);
  }

  // Get current date for selected date
  const selectedDate = getCurrentDateISO();

  return (
    <div className="min-h-screen bg-background text-foreground">
      <Header />
      <div className="px-4 py-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold ">Analytics Dashboard</h1>
          </div>
          {searchTerm && (
            <div className="mb-4">
              <p className="text-sm" style={{ color: 'green' }}>
                Showing results for: <span>{searchTerm}</span>
              </p>
            </div>
          )}
        </div>

        {!hydrated ? (
          <AnalyticsSkeleton />
        ) : (
          <>
            <SearchBar
              searchInput={searchInput}
              setSearchInput={setSearchInput}
              handleSearch={handleSearch}
              handleKeyPress={handleKeyPress}
              isSearching={isSearching}
              searchTerm={searchTerm}
            />
            <div className="mt-4">
              <ConversionMetrics
                searchTerm={searchTerm}
              />
            </div>
            
            <div className="mt-8 bg-card rounded-lg shadow p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold">Transaction Details</h2>
                <Button
                  onClick={() => window.open(`/api/analytics-mongo/export${searchTerm ? `?search=${encodeURIComponent(searchTerm)}` : ''}`, '_blank')}
                  variant="outline"
                  size="default"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export Current View
                </Button>
              </div>
              <AnalyticsTable
                date={selectedDate}
                columns={columns}
                data={analyticsData}
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={handlePageChange}
                searchTerm={searchTerm}
              />
            </div>
          </>
        )}
      </div>
    </div>
  );
}
