import { NextRequest, NextResponse } from 'next/server';
import { connectDB, MasterOTA } from '@/lib/mongodb';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB();
    const masterOta = await MasterOTA.findById(params.id, {
      _id: 1,
      otaName: 1,
      'booking.live': 1,
      'booking.liveDate': 1,
      'booking.lastUsedDate': 1,
      'ancillaries.live': 1,
      'ancillaries.liveDate': 1,
      'ancillaries.lastUsedDate': 1,
      'seats.live': 1,
      'seats.liveDate': 1,
      'seats.lastUsedDate': 1,
      'cancel.live': 1,
      'cancel.liveDate': 1,
      'cancel.lastUsedDate': 1,
      'void.live': 1,
      'void.liveDate': 1,
      'void.lastUsedDate': 1,
      'change.live': 1,
      'change.liveDate': 1,
      'change.lastUsedDate': 1,
      'interline.live': 1,
      'interline.liveDate': 1,
      'interline.lastUsedDate': 1,
      'multiCity.live': 1,
      'multiCity.liveDate': 1,
      'multiCity.lastUsedDate': 1,
      'apis.live': 1,
      'apis.liveDate': 1,
      'apis.lastUsedDate': 1
    });
    
    if (!masterOta) {
      return NextResponse.json({ error: 'Master OTA not found' }, { status: 404 });
    }
    
    return NextResponse.json(masterOta);
  } catch (error) {
    return NextResponse.json({ error: 'Failed to fetch usage data' }, { status: 500 });
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB();
    const rawData = await request.json();
    const usageData: Record<string, any> = {};
    const currentDate = new Date();
    
    // Convert flat structure to nested structure
    const integrationTypes = ['booking', 'ancillaries', 'seats', 'cancel', 'void', 'change', 'interline', 'multiCity', 'apis'];
    
    integrationTypes.forEach(type => {
      if (type in rawData) {
        usageData[type] = {
          live: rawData[type],
          // If setting to false, clear the liveDate and lastUsedDate
          ...(rawData[type] === false ? { 
            liveDate: null,
            lastUsedDate: null 
          } : {
            // If setting to true, update lastUsedDate
            lastUsedDate: currentDate
          })
        };
      }
    });
    
    const masterOta = await MasterOTA.findByIdAndUpdate(
      params.id,
      { $set: usageData },
      { new: true }
    );
    
    if (!masterOta) {
      return NextResponse.json({ error: 'Master OTA not found' }, { status: 404 });
    }
    
    return NextResponse.json(masterOta);
  } catch (error) {
    return NextResponse.json({ error: 'Failed to update usage data' }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB();
    // Reset all integration types to their default state
    const usageFields = {
      'booking.live': false,
      'booking.liveDate': null,
      'booking.lastUsedDate': null,
      'ancillaries.live': false,
      'ancillaries.liveDate': null,
      'ancillaries.lastUsedDate': null,
      'seats.live': false,
      'seats.liveDate': null,
      'seats.lastUsedDate': null,
      'cancel.live': false,
      'cancel.liveDate': null,
      'cancel.lastUsedDate': null,
      'void.live': false,
      'void.liveDate': null,
      'void.lastUsedDate': null,
      'change.live': false,
      'change.liveDate': null,
      'change.lastUsedDate': null,
      'interline.live': false,
      'interline.liveDate': null,
      'interline.lastUsedDate': null,
      'multiCity.live': false,
      'multiCity.liveDate': null,
      'multiCity.lastUsedDate': null,
      'apis.live': false,
      'apis.liveDate': null,
      'apis.lastUsedDate': null
    };
    
    const masterOta = await MasterOTA.findByIdAndUpdate(
      params.id, 
      { $set: usageFields },
      { new: true }
    );
    
    if (!masterOta) {
      return NextResponse.json({ error: 'Master OTA not found' }, { status: 404 });
    }
    
    return NextResponse.json({ message: 'Usage data reset successfully' });
  } catch (error) {
    return NextResponse.json({ error: 'Failed to reset usage data' }, { status: 500 });
  }
}
