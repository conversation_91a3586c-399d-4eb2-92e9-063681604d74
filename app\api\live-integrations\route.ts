import { NextResponse } from 'next/server';
import * as XLSX from 'xlsx';
import { connectDB, Integration, QuickWin, LiveIntegration, MasterOTA } from '@/lib/mongodb';
import { format } from 'date-fns';
import { Document } from 'mongoose';

export const runtime = 'nodejs';

// Define interfaces for our document types
interface BaseDocument {
  _id: string;
  batch: string;
  masterOtaId: {
    otaName: string;
    type: string;
    country: string;
    clientId?: string;
    userId?: string;
  };
  startDate: Date;
  weeks: number;
  weeksElapsed: number;
  status: string;
  manager: string;
  clientId?: string;
  userId?: string;
  archived?: boolean;
  actualEndDate?: Date;
}

interface IntegrationDocument extends BaseDocument {
  _id: string;
}

interface QuickWinDocument extends BaseDocument {
  _id: string;
}

interface FlattenedResult {
  _id: string;
  batch: string;
  ota: string;
  type: string;
  country: string;
  status: string;
  manager: string;
  clientId?: string;
  userId?: string;
  actualEndDate?: string;
  sourceType: 'Integration' | 'QuickWin';
}

export async function GET(request: Request) {
  try {
    await connectDB();
    const { searchParams } = new URL(request.url);
    
    // Get all query parameters
    const search = searchParams.get('search') || '';
    const page = parseInt(searchParams.get('page') || '1', 10);
    const pageSize = parseInt(searchParams.get('pageSize') || '25', 10);
    const isExport = searchParams.get('export') === 'true';
    
    // Get filter parameters
    const batch = searchParams.get('batch') || '';
    const type = searchParams.get('type') || '';
    const status = searchParams.get('status') || 'Live';  // Default to Live status if not specified
    const country = searchParams.get('country') || '';
    const manager = searchParams.get('manager') || '';
    const sourceType = searchParams.get('sourceType') || '';

    // Build search query for both collections
    let searchQuery: any = {};
    
    // First, find masterOtaIds that match the search if it involves OTA names or countries
    let matchingMasterOtaIds: any[] = [];
    if (search) {
      try {
        matchingMasterOtaIds = await MasterOTA.find({
          $or: [
            { otaName: { $regex: search, $options: 'i' } },
            { country: { $regex: search, $options: 'i' } }
          ]
        }).select('_id').lean();
      } catch (err) {
        console.error('Error searching MasterOTA:', err);
      }

      // Build $or query for direct fields and masterOtaId references
      searchQuery.$or = [
        { batch: { $regex: search, $options: 'i' } },
        { manager: { $regex: search, $options: 'i' } }
      ];

      // Add masterOtaId to search if we found any matches
      if (matchingMasterOtaIds.length > 0) {
        searchQuery.$or.push({
          masterOtaId: { $in: matchingMasterOtaIds.map(item => item._id) }
        });
      }
    }
    
    // Add filters if they exist
    const filterQuery: any = {};
    
    if (batch) filterQuery.batch = batch;
    if (status) filterQuery.status = status;
    if (manager) filterQuery.manager = manager;
    
    // These filters need to be applied differently since we're using populate
    if (type || country) {
      try {
        const masterOtaQuery: any = {};
        if (type) masterOtaQuery.type = type;
        if (country) masterOtaQuery.country = country;
        
        const matchingOtaIds = await MasterOTA.find(masterOtaQuery).select('_id').lean();
        filterQuery.masterOtaId = { $in: matchingOtaIds.map(item => item._id) };
      } catch (err) {
        console.error('Error filtering by MasterOTA fields:', err);
      }
    }

    // Combine all query parts
    const query = {
      ...searchQuery,
      ...filterQuery
    };

    // Count total documents from both collections
    let integrationsCount = 0;
    let quickWinsCount = 0;
    
    // Only count from Integration collection if not filtering by sourceType or if sourceType is Integration
    if (!sourceType || sourceType === 'Integration') {
      integrationsCount = await Integration.countDocuments(query);
    }
    
    // Only count from QuickWin collection if not filtering by sourceType or if sourceType is QuickWin
    if (!sourceType || sourceType === 'QuickWin') {
      quickWinsCount = await QuickWin.countDocuments(query);
    }

    const total = integrationsCount + quickWinsCount;
    const totalPages = Math.ceil(total / pageSize);

    // Calculate skip and limit for pagination across both collections
    const skip = (page - 1) * pageSize;

    // Fetch data from both collections with populated masterOtaId
    let integrations: IntegrationDocument[] = [];
    let quickWins: QuickWinDocument[] = [];

    if ((skip < integrationsCount) && (!sourceType || sourceType === 'Integration')) {
      integrations = (await Integration.find(query)
        .skip(skip)
        .limit(pageSize)
        .populate('masterOtaId', 'otaName type country clientId userId')
        .lean()) as unknown as IntegrationDocument[];
    }

    if ((integrations.length < pageSize) && (!sourceType || sourceType === 'QuickWin')) {
      const quickWinsSkip = Math.max(0, skip - integrationsCount);
      const quickWinsLimit = pageSize - integrations.length;
      quickWins = (await QuickWin.find(query)
        .skip(quickWinsSkip)
        .limit(quickWinsLimit)
        .populate('masterOtaId', 'otaName type country clientId userId')
        .lean()) as unknown as QuickWinDocument[];
    }

    // Combine and format the results, flattening the masterOtaId data
    const combinedResults: FlattenedResult[] = [
      ...integrations.map(item => ({
        _id: item._id,
        batch: item.batch,
        ota: item.masterOtaId?.otaName || 'N/A',
        type: item.masterOtaId?.type || 'N/A',
        country: item.masterOtaId?.country || 'N/A',
        status: item.status,
        manager: item.manager,
        clientId: item.masterOtaId?.clientId,
        userId: item.masterOtaId?.userId,
        actualEndDate: item.actualEndDate ? format(new Date(item.actualEndDate), 'yyyy-MM-dd') : undefined,
        sourceType: 'Integration' as const
      })),
      ...quickWins.map(item => ({
        _id: item._id,
        batch: item.batch,
        ota: item.masterOtaId?.otaName || 'N/A',
        type: item.masterOtaId?.type || 'N/A',
        country: item.masterOtaId?.country || 'N/A',
        status: item.status,
        manager: item.manager,
        clientId: item.masterOtaId?.clientId,
        userId: item.masterOtaId?.userId,
        actualEndDate: item.actualEndDate ? format(new Date(item.actualEndDate), 'yyyy-MM-dd') : undefined,
        sourceType: 'QuickWin' as const
      }))
    ];

    // If exporting, generate Excel file
    if (isExport) {
      // For exports, we should get all matching records, not just the paginated ones
      const allIntegrations = (!sourceType || sourceType === 'Integration') 
        ? (await Integration.find(query).populate('masterOtaId', 'otaName type country').lean()) as unknown as IntegrationDocument[]
        : [];
        
      const allQuickWins = (!sourceType || sourceType === 'QuickWin')
        ? (await QuickWin.find(query).populate('masterOtaId', 'otaName type country').lean()) as unknown as QuickWinDocument[]
        : [];
        
      const allCombinedResults: FlattenedResult[] = [
        ...allIntegrations.map(item => ({
          _id: item._id,
          batch: item.batch,
          ota: item.masterOtaId?.otaName || 'N/A',
          type: item.masterOtaId?.type || 'N/A',
          country: item.masterOtaId?.country || 'N/A',
          status: item.status,
          manager: item.manager,
          clientId: item.masterOtaId?.clientId,
          userId: item.masterOtaId?.userId,
          actualEndDate: item.actualEndDate ? format(new Date(item.actualEndDate), 'yyyy-MM-dd') : undefined,
          sourceType: 'Integration' as const
        })),
        ...allQuickWins.map(item => ({
          _id: item._id,
          batch: item.batch,
          ota: item.masterOtaId?.otaName || 'N/A',
          type: item.masterOtaId?.type || 'N/A',
          country: item.masterOtaId?.country || 'N/A',
          status: item.status,
          manager: item.manager,
          clientId: item.masterOtaId?.clientId,
          userId: item.masterOtaId?.userId,
          actualEndDate: item.actualEndDate ? format(new Date(item.actualEndDate), 'yyyy-MM-dd') : undefined,
          sourceType: 'QuickWin' as const
        }))
      ];
      
      const workbook = XLSX.utils.book_new();
      const worksheet = XLSX.utils.json_to_sheet(allCombinedResults);
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Live Integrations');
      
      const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
      
      const headers = new Headers();
      headers.append('Content-Disposition', `attachment; filename="live-integrations-${format(new Date(), 'yyyy-MM-dd')}.xlsx"`);
      headers.append('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      
      return new NextResponse(buffer, { headers });
    }

    // Return JSON data for regular API requests
    return NextResponse.json({
      data: combinedResults,
      total,
      page,
      pageSize,
      totalPages
    });

  } catch (error) {
    console.error('Error fetching live integrations:', error);
    return NextResponse.json({ error: 'Failed to fetch live integrations data' }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    await connectDB();
    
    const body = await request.json();
    
    // Validate required fields
    const requiredFields = ['ota', 'batch', 'type', 'country', 'status'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }
    
    // Create new live integration record
    const newIntegration = new LiveIntegration({
      ...body,
      createdAt: new Date(),
      updatedAt: new Date()
    });
    
    await newIntegration.save();
    
    return NextResponse.json(
      { message: 'Live integration created successfully', data: newIntegration },
      { status: 201 }
    );
    
  } catch (error) {
    console.error('Error creating live integration:', error);
    return NextResponse.json(
      { error: 'Failed to create live integration' },
      { status: 500 }
    );
  }
} 