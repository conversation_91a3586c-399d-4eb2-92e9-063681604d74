'use client';

import { login } from '../../lib/auth';
import { Button } from '@/components/ui/button';
import { useState } from 'react';
import Image from 'next/image';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { ModeToggle } from '@/components/mode-toggle';
import { LogIn } from 'lucide-react';

export default function LoginPage() {
  const [isLoading, setIsLoading] = useState(false);

  const handleLogin = async () => {
    setIsLoading(true);
    try {
      await login();
    } catch (error) {
      console.error('Login failed:', error);
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-b from-blue-50 to-white dark:from-zinc-900 dark:to-zinc-800 p-4">
      <div className="absolute top-4 right-4">
        <ModeToggle />
      </div>

      <Card className="max-w-md w-full shadow-xl border-t-4 border-t-[#0851a7] dark:border-t-blue-500">
        <CardHeader className="space-y-1 flex flex-col items-center">
          <div className="w-40 h-16 relative mb-4 bg-[#0851a7] rounded-md flex items-center justify-center">
            <Image
              src="https://www.flydubai.com/v2/_next/static/media/iconF.661174c2.svg"
              alt="flydubai logo"
              width={120}
              height={40}
              priority
              className="object-contain"
            />
          </div>
          <CardTitle className="text-2xl font-bold text-center">OTA Integration Dashboard</CardTitle>
          <CardDescription className="text-center">
            Manage and monitor Online Travel Agency integrations
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-4">
          <div className="bg-blue-50 dark:bg-zinc-800 p-4 rounded-md text-sm">
            <h3 className="font-medium text-[#0851a7] dark:text-blue-400 mb-2">About This Application</h3>
            <p className="text-gray-700 dark:text-gray-300 mb-2">
              The OTA Integration Dashboard helps you track and manage integrations with various Online Travel Agencies.
            </p>
            <ul className="list-disc list-inside text-gray-600 dark:text-gray-400 space-y-1">
              <li>Monitor integration status</li>
              <li>Track quick wins and sub-integrations</li>
              <li>Generate reports and analytics</li>
              <li>Manage OTA hub connections</li>
            </ul>
          </div>

          <Button
            onClick={handleLogin}
            className="w-full flex justify-center py-6"
            size="lg"
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <span className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
                Signing in...
              </>
            ) : (
              <>
                <LogIn className="mr-2 h-5 w-5" />
                Sign In with flydubai Sprint SSO
              </>
            )}
          </Button>
        </CardContent>

        <CardFooter className="flex flex-col space-y-2">
          <p className="text-xs text-center text-gray-500 dark:text-gray-400">
            This application uses your flydubai credentials for authentication.
            No additional login information is required.
          </p>
          <p className="text-xs text-center text-gray-400 dark:text-gray-500">
            © {new Date().getFullYear()} flydubai. All rights reserved.
          </p>
        </CardFooter>
      </Card>
    </div>
  );
}