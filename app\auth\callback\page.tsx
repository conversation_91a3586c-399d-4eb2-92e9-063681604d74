'use client';

import { useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { handleCallback } from '../../../lib/auth';

export default function CallbackPage() {
  console.log('callback success');
  const router = useRouter();
  const hasRun = useRef(false); // Track if the effect has already run

  useEffect(() => {
    if (hasRun.current) return; // Prevent duplicate execution
    hasRun.current = true;

    const processCallback = async () => {
      try {
        //console.log('Processing callback...');
        await handleCallback();
        router.push('/'); // Redirect to the dashboard
        //console.log('Callback processed successfully. Redirecting to dashboard...');
      } catch (error) {
        console.error('Error processing callback:', error);
        router.push('/login');
      }
    };

    processCallback();
  }, [router]);

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-2xl font-semibold mb-4">Processing login...</h1>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
      </div>
    </div>
  );
}