import { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, Card<PERSON>itle, CardDescription } from "@/components/ui/card";
import { ArrowUp, BarChart3 } from "lucide-react";
import { formatLargeNumber, formatOTAName } from "@/lib/format-utils";
import { cn } from "@/lib/utils";

interface OTAData {
  OTAName: string;
  TotalLooks: number;
  SuccessLooks: number;
  TotalBookings: number;
  LookToBookRatio: number;
}

const HighestLookToBookOTAsCard = () => {
  const [data, setData] = useState<OTAData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        // Adding a timestamp parameter to avoid caching issues
        const timestamp = new Date().getTime();
        const response = await fetch(`/api/analytics-mongo/highest-looktobook-otas?_=${timestamp}`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch data');
        }
        
        const result = await response.json();
        
        if (result.success && result.data) {
          setData(result.data);
        } else {
          setError(result.message || 'Failed to retrieve data');
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
        console.error('Error fetching highest look-to-book OTAs:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-purple-900">Highest Look-to-Book Ratios</CardTitle>
            <CardDescription>OTAs with most searches per booking</CardDescription>
          </div>
          <BarChart3 className="h-4 w-4 text-purple-500" />
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex justify-center items-center h-48">
            <p className="text-sm text-muted-foreground">Loading...</p>
          </div>
        ) : error ? (
          <div className="flex justify-center items-center h-48">
            <p className="text-sm text-red-500">{error}</p>
          </div>
        ) : data.length === 0 ? (
          <div className="flex justify-center items-center h-48">
            <p className="text-sm text-muted-foreground">No data available</p>
          </div>
        ) : (          <div className="space-y-2 mt-2">
            {data.map((ota, index) => (
              <div key={ota.OTAName} className="pb-1.5 border-b border-dashed last:border-0 last:pb-0">
                <div className="flex justify-between items-center">
                  <div className="flex items-center space-x-2">
                    <div className={cn(
                      "font-medium truncate",
                      index === 0 && "text-purple-900",
                      index === 1 && "text-purple-800",
                      index === 2 && "text-purple-700"
                    )} title={ota.OTAName}>
                      {formatOTAName(ota.OTAName)}
                    </div>
                    {index === 0 && (
                      <div className="bg-purple-100 text-purple-800 text-xs px-1.5 py-0.5 rounded flex items-center">
                        <ArrowUp className="h-3 w-3 mr-0.5" />
                        Top
                      </div>
                    )}
                  </div>
                  <div className="text-sm">
                    <span className={cn(
                      "font-medium",
                      index === 0 && "text-purple-900",
                      index === 1 && "text-purple-800",
                      index === 2 && "text-purple-700"
                    )}>
                      {ota.LookToBookRatio > 0 ? `${ota.LookToBookRatio.toFixed(0)}:1` : 'N/A'}
                    </span>
                    <span className="text-muted-foreground text-xs ml-1">ratio</span>
                  </div>
                </div>
                <div className="flex justify-between items-center text-xs text-muted-foreground mt-0.5">
                  <div>
                    {formatLargeNumber(ota.SuccessLooks || 0)} looks
                    <span className="mx-1">•</span>
                    {formatLargeNumber(ota.TotalBookings || 0)} bookings
                  </div>
                  <div className={cn(
                    index === 0 && "text-purple-600",
                    index === 1 && "text-purple-500",
                    index === 2 && "text-purple-400"
                  )}>
                    {ota.TotalBookings > 0 ? `${((ota.SuccessLooks / ota.TotalLooks) * 100).toFixed(1)}%` : 'N/A'}</div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default HighestLookToBookOTAsCard;
