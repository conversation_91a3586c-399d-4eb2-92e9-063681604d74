"use client";

import { UserManager, WebStorageStateStore } from 'oidc-client-ts';

const settings = {
  authority: 'http://uat3-sprintauthv2.np.flydubai.com',
  client_id: 'OTADASH_FZ_P',
  redirect_uri: typeof window !== 'undefined' ? `${window.location.origin}/auth/callback` : '',
  response_type: 'code',
  scope: 'otadashboard openid profile offline_access',
  userStore: typeof window !== 'undefined'
    ? new WebStorageStateStore({ store: window.localStorage })
    : undefined, // Prevent SSR from accessing `window`
  loadUserInfo: false,
  monitorSession: false,
  metadata: {
    authorization_endpoint: 'https://uat3-sprintauthv2.np.flydubai.com/connect/authorize',
    token_endpoint: 'https://uat3-sprintauthv2.np.flydubai.com/connect/token',
    end_session_endpoint: 'https://uat3-sprintauthv2.np.flydubai.com/connect/endsession',
    issuer: 'https://uat3-sprintauthv2.np.flydubai.com',
    jwks_uri: 'https://uat3-sprintauthv2.np.flydubai.com/.well-known/openid-configuration/jwks',
  },
};

let userManager: UserManager | null = null;

export const getUserManager = () => {
  if (typeof window === 'undefined') return null;

  if (!userManager) {
    userManager = new UserManager(settings);
  }
  return userManager;
};

export const login = async () => {
  //console.log('Login function called'); // Debug log
  const manager = getUserManager();
  if (!manager) return;

  try {
    await manager.signinRedirect();
  } catch (error) {
    console.error('Login failed:', error);
    throw error;
  }
};

export const logout = async () => {
  const manager = getUserManager();
  if (!manager) return;

  try {
    await manager.signoutRedirect();
  } catch (error) {
    console.error('Logout failed:', error);
    throw error;
  }
};

export const handleCallback = async () => {
  console.log('Handling callback...'); // Debug log
  const manager = getUserManager();
  console.log('Handling callback...', manager); // Debug log
  if (!manager) throw new Error('UserManager not initialized');

  try {
    const user = await manager.signinRedirectCallback();
    document.cookie = `id_token=${user.id_token}; path=/; secure; SameSite=Strict`;
    console.log('User after callback:', user); // Debug log
    return user;
  } catch (error) {
    console.error('Error handling callback:', error);
    throw error;
  }
};

export const getUser = async () => {
  const manager = getUserManager();
  if (!manager) return null;

  try {
    const user = await manager.getUser();
    return user;
  } catch (error) {
    console.error('Error getting user:', error);
    return null;
  }
};