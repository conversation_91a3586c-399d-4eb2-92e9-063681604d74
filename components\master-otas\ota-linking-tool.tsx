'use client';

import { useState, useEffect, useMemo } from 'react';
import { MasterOTA, Integration, QuickWin, SubIntegration } from '@/types';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from 'sonner';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Link, LinkIcon } from 'lucide-react';

type EntityType = 'Integration' | 'QuickWin' | 'SubIntegration' | 'LiveIntegration';

interface OtaLinkingToolProps {
  masterOtas: MasterOTA[];
  onSuccess?: () => void;
}

export function OtaLinkingTool({ masterOtas, onSuccess }: OtaLinkingToolProps) {
  const [selectedMasterOta, setSelectedMasterOta] = useState<string>('');
  const [entityType, setEntityType] = useState<EntityType>('Integration');
  const [entities, setEntities] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isLinking, setIsLinking] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedEntities, setSelectedEntities] = useState<string[]>([]);

  // Fetch entities when entityType changes
  useEffect(() => {
    if (entityType) {
      fetchEntities(entityType);
    }
  }, [entityType]);

  const fetchEntities = async (type: EntityType) => {
    setIsLoading(true);
    try {
      let endpoint = '';
      switch (type) {
        case 'Integration':
          endpoint = '/api/integrations';
          break;
        case 'QuickWin':
          endpoint = '/api/quickwins';
          break;
        case 'SubIntegration':
          endpoint = '/api/subintegrations';
          break;
        case 'LiveIntegration':
          endpoint = '/api/live-integrations';
          break;
      }
      
      console.log(`Fetching entities from endpoint: ${endpoint}`);
      const response = await fetch(endpoint);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch ${type}s: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      console.log(`Received data from ${endpoint}:`, data);
      
      // Handle both array response and paginated response with data property
      const entities = Array.isArray(data) ? data : data.data || [];
      console.log(`Extracted ${entities.length} entities`);
      
      setEntities(entities);
    } catch (error) {
      console.error(`Error fetching ${type}s:`, error);
      toast.error(`Failed to load ${type}s`);
      setEntities([]); // Reset to empty array on error
    } finally {
      setIsLoading(false);
    }
  };

  const handleMasterOtaChange = (value: string) => {
    setSelectedMasterOta(value);
  };

  const handleEntityTypeChange = (value: EntityType) => {
    setEntityType(value);
    setSelectedEntities([]);
  };

  const handleCheckboxChange = (id: string) => {
    setSelectedEntities(prev => {
      if (prev.includes(id)) {
        return prev.filter(item => item !== id);
      } else {
        return [...prev, id];
      }
    });
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const filteredIds = filteredEntities.map(entity => entity._id);
      setSelectedEntities(filteredIds);
    } else {
      setSelectedEntities([]);
    }
  };

  const handleLinkEntities = async () => {
    if (!selectedMasterOta || selectedEntities.length === 0) {
      toast.error('Please select a Master OTA and at least one entity to link');
      return;
    }

    setIsLinking(true);
    try {
      const response = await fetch('/api/master-otas/link', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          masterOtaId: selectedMasterOta,
          entityType,
          entityIds: selectedEntities,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to link entities to Master OTA`);
      }

      const result = await response.json();
      toast.success(`Successfully linked ${result.modifiedCount} ${entityType}(s) to Master OTA`);
      
      // Refresh the entities list
      fetchEntities(entityType);
      setSelectedEntities([]);
      
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error linking entities:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to link entities');
    } finally {
      setIsLinking(false);
    }
  };

  // Filter entities based on search term
  const filteredEntities = useMemo(() => {
    console.log('Filtering entities:', { 
      isArray: Array.isArray(entities),
      entitiesLength: entities?.length || 0,
      searchTerm
    });
    
    if (!Array.isArray(entities) || entities.length === 0) {
      return [];
    }

    return entities.filter(entity => {
      if (!entity) return false;
      
      const searchFields = ['ota', 'batch', 'country', 'iata'].filter(field => entity && entity[field]);
      return searchFields.some(field => 
        entity[field].toLowerCase().includes(searchTerm.toLowerCase())
      );
    });
  }, [entities, searchTerm]);

  // Check if all filtered entities are selected
  const allSelected = filteredEntities.length > 0 && 
    filteredEntities.every(entity => selectedEntities.includes(entity._id));

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label htmlFor="master-ota-select">Select Master OTA</Label>
          <Select 
            value={selectedMasterOta} 
            onValueChange={handleMasterOtaChange}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select a Master OTA" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectLabel>Master OTAs</SelectLabel>
                {masterOtas.map((ota) => (
                  <SelectItem key={ota._id} value={ota._id}>
                    {ota.otaName} ({ota.country})
                  </SelectItem>
                ))}
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="entity-type-select">Entity Type</Label>
          <Select 
            value={entityType} 
            onValueChange={(value) => handleEntityTypeChange(value as EntityType)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select entity type" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectLabel>Entity Types</SelectLabel>
                <SelectItem value="Integration">Integrations</SelectItem>
                <SelectItem value="QuickWin">Quick Wins</SelectItem>
                <SelectItem value="SubIntegration">Sub-Integrations</SelectItem>
                <SelectItem value="LiveIntegration">Live Integrations</SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <Input
            placeholder={`Search ${entityType}s...`}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-sm"
          />
          <div className="flex items-center gap-4">
            <div className="text-sm text-muted-foreground">
              {selectedEntities.length} of {filteredEntities.length} selected
            </div>
            <Button 
              onClick={handleLinkEntities} 
              disabled={isLinking || selectedEntities.length === 0 || !selectedMasterOta}
              className="gap-2"
            >
              <LinkIcon className="h-4 w-4" />
              {isLinking ? 'Linking...' : 'Link Selected'}
            </Button>
          </div>
        </div>

        {isLoading ? (
          <div className="text-center p-6">Loading {entityType}s...</div>
        ) : filteredEntities.length === 0 ? (
          <div className="text-center p-6 border rounded-md">
            {searchTerm
              ? `No ${entityType}s match your search criteria`
              : `No ${entityType}s available`}
          </div>
        ) : (
          <div className="border rounded-md">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">
                    <Checkbox 
                      checked={allSelected} 
                      onCheckedChange={handleSelectAll} 
                    />
                  </TableHead>
                  <TableHead>OTA Name</TableHead>
                  <TableHead>Batch</TableHead>
                  <TableHead>Country</TableHead>
                  <TableHead>IATA</TableHead>
                  <TableHead>Master OTA</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredEntities.map((entity) => (
                  <TableRow key={entity._id}>
                    <TableCell>
                      <Checkbox 
                        checked={selectedEntities.includes(entity._id)} 
                        onCheckedChange={() => handleCheckboxChange(entity._id)} 
                      />
                    </TableCell>
                    <TableCell className="font-medium">{entity.ota}</TableCell>
                    <TableCell>{entity.batch}</TableCell>
                    <TableCell>{entity.country}</TableCell>
                    <TableCell>{entity.iata}</TableCell>
                    <TableCell>
                      {entity.masterOtaId ? (
                        <div className="flex items-center gap-1">
                          <LinkIcon className="h-4 w-4 text-blue-500" />
                          <span>Linked</span>
                        </div>
                      ) : (
                        <span className="text-muted-foreground">Not linked</span>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </div>
    </div>
  );
} 