import { NextResponse } from 'next/server';
import { connectDB, Integration } from '@/lib/mongodb';

export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

export async function GET(request: Request) {
  try {
    await connectDB();
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search') || '';
    const page = parseInt(searchParams.get('page') || '1', 10);
    const pageSize = parseInt(searchParams.get('pageSize') || '10', 10);
    
    // Build search query with archived: true filter - explicitly set
    const query = {
      archived: true, // Explicitly include archived items
      ...(search ? {
        $or: [
          { batch: { $regex: search, $options: 'i' } },
          { ota: { $regex: search, $options: 'i' } },
          { country: { $regex: search, $options: 'i' } },
          { status: { $regex: search, $options: 'i' } }
        ]
      } : {})
    };

    // Count total documents for pagination
    const total = await Integration.countDocuments(query);
    const totalPages = Math.ceil(total / pageSize);

    // Fetch archives with pagination and sort by archivedDate descending
    const archivedIntegrations = await Integration.find(query)
      .sort({ archivedDate: -1 })
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .lean();

    return NextResponse.json({
      data: archivedIntegrations,
      total,
      page,
      pageSize,
      totalPages
    });
    
  } catch (error) {
    console.error('Error fetching archived integrations:', error);
    return NextResponse.json({ error: 'Failed to fetch archived integrations' }, { status: 500 });
  }
} 