"use client";

import { MasterOT<PERSON> } from "@/types";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { mutate } from "swr";

interface UsageTableProps {
  usages: MasterOTA[];
  onEdit: (usage: MasterOTA) => void;
}

export function UsageTable({ usages, onEdit }: UsageTableProps) {
  const handleDelete = async (id: string) => {
    if (!confirm("Are you sure you want to delete this usage?")) return;
    try {
      await fetch(`/api/usages/${id}`, { method: "DELETE" });
      mutate("/api/usages"); // Refresh the data
    } catch (error) {
      console.error("Failed to delete usage:", error);
    }
  };

  const renderBooleanValue = (value: boolean) => (value ? "✅" : "❌");

  const getIntegrationStatus = (integration: any) => {
    // Add debugging to see what we're receiving
    console.log('Integration status:', integration);
    // Check if integration exists and has a live property
    if (!integration) return false;
    // Handle both direct boolean values and integration type objects
    return typeof integration === 'boolean' ? integration : integration.live ?? false;
  };

  return (
    <div className="w-full">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="font-semibold">OTA Name</TableHead>
              <TableHead className="font-semibold">Booking</TableHead>
              <TableHead className="font-semibold">Ancillaries</TableHead>
              <TableHead className="font-semibold">Seats</TableHead>
              <TableHead className="font-semibold">Cancel</TableHead>
              <TableHead className="font-semibold">Void</TableHead>
              <TableHead className="font-semibold">Change</TableHead>
              <TableHead className="font-semibold">Interline</TableHead>
              <TableHead className="font-semibold">Multi-City</TableHead>
              <TableHead className="font-semibold">APIS</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {usages.length === 0 ? (
              <TableRow>
                <TableCell colSpan={10} className="text-center py-4">
                  No API usages found
                </TableCell>
              </TableRow>
            ) : (
              usages.map((usage) => (
                <TableRow 
                  key={usage._id} 
                  className="cursor-pointer hover:bg-muted/50" 
                  onClick={() => onEdit(usage)}
                >
                  <TableCell>{usage.otaName}</TableCell>
                  <TableCell>{renderBooleanValue(getIntegrationStatus(usage.booking))}</TableCell>
                  <TableCell>{renderBooleanValue(getIntegrationStatus(usage.ancillaries))}</TableCell>
                  <TableCell>{renderBooleanValue(getIntegrationStatus(usage.seats))}</TableCell>
                  <TableCell>{renderBooleanValue(getIntegrationStatus(usage.cancel))}</TableCell>
                  <TableCell>{renderBooleanValue(getIntegrationStatus(usage.void))}</TableCell>
                  <TableCell>{renderBooleanValue(getIntegrationStatus(usage.change))}</TableCell>
                  <TableCell>{renderBooleanValue(getIntegrationStatus(usage.interline))}</TableCell>
                  <TableCell>{renderBooleanValue(getIntegrationStatus(usage.multiCity))}</TableCell>
                  <TableCell>{renderBooleanValue(getIntegrationStatus(usage.apis))}</TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}