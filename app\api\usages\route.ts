import { NextRequest, NextResponse } from 'next/server';
import { connectDB, MasterOTA } from '@/lib/mongodb';
import * as XLSX from 'xlsx';
import { format } from 'date-fns';

export const runtime = 'nodejs';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const isExport = searchParams.get('export') === 'true';
    
    await connectDB();
    const masterOtas = await MasterOTA.find({}, {
      _id: 1,
      otaName: 1,
      'booking.live': 1,
      'booking.liveDate': 1,
      'booking.lastUsedDate': 1,
      'ancillaries.live': 1,
      'ancillaries.liveDate': 1,
      'ancillaries.lastUsedDate': 1,
      'seats.live': 1,
      'seats.liveDate': 1,
      'seats.lastUsedDate': 1,
      'cancel.live': 1,
      'cancel.liveDate': 1,
      'cancel.lastUsedDate': 1,
      'void.live': 1,
      'void.liveDate': 1,
      'void.lastUsedDate': 1,
      'change.live': 1,
      'change.liveDate': 1,
      'change.lastUsedDate': 1,
      'interline.live': 1,
      'interline.liveDate': 1,
      'interline.lastUsedDate': 1,
      'multiCity.live': 1,
      'multiCity.liveDate': 1,
      'multiCity.lastUsedDate': 1,
      'apis.live': 1,
      'apis.liveDate': 1,
      'apis.lastUsedDate': 1
    });
    
    // If exporting, generate Excel file
    if (isExport) {
      // Prepare data for Excel export
      const exportData = masterOtas.map(ota => {
        const formatDate = (date: Date | null | undefined) => {
          return date ? format(new Date(date), 'yyyy-MM-dd') : 'N/A';
        };
        
        return {
          'OTA Name': ota.otaName,
          'Booking': ota.booking?.live ? 'Yes' : 'No',
          //'Booking Live Date': formatDate(ota.booking?.liveDate),
          //'Booking Last Used': formatDate(ota.booking?.lastUsedDate),
          'Ancillaries': ota.ancillaries?.live ? 'Yes' : 'No',
          //'Ancillaries Live Date': formatDate(ota.ancillaries?.liveDate),
          //'Ancillaries Last Used': formatDate(ota.ancillaries?.lastUsedDate),
          'Seats': ota.seats?.live ? 'Yes' : 'No',
          //'Seats Live Date': formatDate(ota.seats?.liveDate),
          //'Seats Last Used': formatDate(ota.seats?.lastUsedDate),
          'Cancel': ota.cancel?.live ? 'Yes' : 'No',
          //'Cancel Live Date': formatDate(ota.cancel?.liveDate),
          //'Cancel Last Used': formatDate(ota.cancel?.lastUsedDate),
          'Void': ota.void?.live ? 'Yes' : 'No',
          //'Void Live Date': formatDate(ota.void?.liveDate),
          //'Void Last Used': formatDate(ota.void?.lastUsedDate),
          'Change': ota.change?.live ? 'Yes' : 'No',
          //'Change Live Date': formatDate(ota.change?.liveDate),
          // 'Change Last Used': formatDate(ota.change?.lastUsedDate),
          'Interline': ota.interline?.live ? 'Yes' : 'No',
          //'Interline Live Date': formatDate(ota.interline?.liveDate),
          //'Interline Last Used': formatDate(ota.interline?.lastUsedDate),
          'Multi-City': ota.multiCity?.live ? 'Yes' : 'No',
          //'Multi-City Live Date': formatDate(ota.multiCity?.liveDate),
          //'Multi-City Last Used': formatDate(ota.multiCity?.lastUsedDate),
          'APIS': ota.apis?.live ? 'Yes' : 'No',
          //'APIS Live Date': formatDate(ota.apis?.liveDate),
          //'APIS Last Used': formatDate(ota.apis?.lastUsedDate)
        };
      });
      
      const workbook = XLSX.utils.book_new();
      const worksheet = XLSX.utils.json_to_sheet(exportData);
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Integration Modules');
      
      const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
      
      return new NextResponse(buffer, {
        headers: {
          'Content-Disposition': 'attachment; filename="integration-modules.xlsx"',
          'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        }
      });
    }
    
    return NextResponse.json(masterOtas);
  } catch (error) {
    return NextResponse.json({ error: 'Failed to fetch usages' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectDB();
    const data = await request.json();
    const { otaId, ...rawData } = data;
    
    if (!otaId) {
      return NextResponse.json({ error: 'OTA ID is required' }, { status: 400 });
    }

    // Convert flat structure to nested structure
    const usageData: Record<string, any> = {};
    const integrationTypes = ['booking', 'ancillaries', 'seats', 'cancel', 'void', 'change', 'interline', 'multiCity', 'apis'];
    const currentDate = new Date();
    
    integrationTypes.forEach(type => {
      if (type in rawData) {
        usageData[type] = {
          live: rawData[type],
          // If setting to false, clear the liveDate
          ...(rawData[type] === false ? { 
            liveDate: null,
            lastUsedDate: null 
          } : {
            // If setting to true, update lastUsedDate
            lastUsedDate: currentDate
          })
        };
      }
    });
    
    const masterOta = await MasterOTA.findByIdAndUpdate(
      otaId,
      { $set: usageData },
      { new: true }
    );
    
    if (!masterOta) {
      return NextResponse.json({ error: 'Master OTA not found' }, { status: 404 });
    }
    
    return NextResponse.json(masterOta, { status: 200 });
  } catch (error) {
    return NextResponse.json({ error: 'Failed to update usage' }, { status: 500 });
  }
}
