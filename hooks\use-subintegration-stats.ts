'use client';

import { useState, useMemo } from 'react';
import useS<PERSON> from 'swr';
import { 
  SubIntegration, 
  SubIntegrationFlow, 
  SubIntegrationStatus, 
  SubIntegrationStats,
  subIntegrationFlows
} from '@/types';
import { fetcher } from '@/lib/api-utils';

/**
 * Custom hook for managing SubIntegration stats
 * @returns Stats data and related state/functions
 */
export function useSubIntegrationStats() {
  const [filteredData, setFilteredData] = useState<SubIntegration[]>([]);
  
  // Fetch stats from API
  const { data: stats, error: statsError } = useSWR<SubIntegrationStats>('/api/subintegrations/stats', fetcher);

  /**
   * Calculate flow statistics based on filtered data
   */
  const calculateFlowStats = (data: SubIntegration[]) => {
    if (!data || data.length === 0) {
      return {
        total: 0,
        byFlow: subIntegrationFlows.reduce((acc, flow) => {
          acc[flow] = 0;
          return acc;
        }, {} as Record<SubIntegrationFlow, number>),
      };
    }

    const byFlow = subIntegrationFlows.reduce((acc, flow) => {
      acc[flow] = 0;
      return acc;
    }, {} as Record<SubIntegrationFlow, number>);

    data.forEach(subintegration => {
      if (byFlow.hasOwnProperty(subintegration.flow as SubIntegrationFlow)) {
        byFlow[subintegration.flow as SubIntegrationFlow]++;
      }
    });

    return {
      total: data.length,
      byFlow,
    };
  };

  /**
   * Calculate status statistics including average completion time and delayed integrations
   */
  const calculateFilteredStats = (data: SubIntegration[]): SubIntegrationStats => {
    if (!data || data.length === 0) {
      return {
        total: 0,
        byStatus: {
          Initiation: 0,
          Integration: 0,
          Testing: 0,
          Demo: 0,
          Live: 0,
          Hold: 0,
        },
        averageCompletionTime: 0,
        delayedIntegrations: 0,
      };
    }

    const byStatus: Record<SubIntegrationStatus, number> = {
      Initiation: 0,
      Integration: 0,
      Testing: 0,
      Demo: 0,
      Live: 0,
      Hold: 0,
    };

    let completedCount = 0;
    let totalCompletionTime = 0;
    let delayedCount = 0;

    data.forEach(subintegration => {
      // Count by status
      if (byStatus.hasOwnProperty(subintegration.status)) {
        byStatus[subintegration.status as SubIntegrationStatus]++;
      }

      // Calculate completion time for completed integrations
      if (subintegration.actualEndDate) {
        completedCount++;
        const startDate = new Date(subintegration.startDate);
        const endDate = new Date(subintegration.actualEndDate);
        totalCompletionTime += (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24 * 7); // Convert to weeks
      }

      // Check for delayed integrations
      if (subintegration.status !== 'Live' && subintegration.expectedEndDate) {
        const currentDate = new Date();
        const expectedEndDate = new Date(subintegration.expectedEndDate);
        if (currentDate > expectedEndDate) {
          delayedCount++;
        }
      }
    });

    return {
      total: data.length,
      byStatus,
      averageCompletionTime: completedCount > 0 ? totalCompletionTime / completedCount : 0,
      delayedIntegrations: delayedCount,
    };
  };

  // Calculate display flow stats based on filtered data or original stats
  const displayFlowStats = useMemo(() => {
    return filteredData.length > 0
      ? calculateFlowStats(filteredData)
      : stats ? calculateFlowStats(filteredData.length > 0 ? filteredData : []) : { total: 0, byFlow: {} as Record<SubIntegrationFlow, number> };
  }, [filteredData, stats]);

  // Calculate display stats based on filtered data or original stats
  const displayStats = useMemo(() => {
    return filteredData.length > 0
      ? calculateFilteredStats(filteredData)
      : stats || calculateFilteredStats([]);
  }, [filteredData, stats]);

  return {
    stats,
    statsError,
    filteredData,
    setFilteredData,
    displayStats,
    displayFlowStats,
    isLoading: !stats && !statsError,
  };
}
