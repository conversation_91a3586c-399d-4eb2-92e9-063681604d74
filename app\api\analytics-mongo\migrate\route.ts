import { NextResponse } from "next/server";
import fs from "fs";
import path from "path";
import { connectDB, Looks, Books } from "../../../../lib/mongodb";

export const runtime = "nodejs";
export const dynamic = 'force-dynamic';

// Add a function to normalize dates for consistent formatting
const normalizeDate = (dateStr: string): Date => {
  try {
    // Handle various date formats (dd-MMM-yy, MM/dd/yyyy, etc.)
    const parts = dateStr.split(/[-\/]/);
    
    // If format is like 01-Apr-25 or 01/Apr/25
    if (parts.length === 3 && isNaN(parseInt(parts[1]))) {
      const day = parseInt(parts[0]);
      const month = ['jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec']
        .indexOf(parts[1].toLowerCase().substring(0, 3));
      let year = parseInt(parts[2]);
      if (year < 100) {
        year = year < 50 ? 2000 + year : 1900 + year;
      }
      return new Date(year, month, day);
    }
    
    // For other formats, create a Date object
    return new Date(dateStr);
  } catch (error) {
    console.warn(`Error normalizing date: ${dateStr}`, error);
    return new Date(); // Return current date if parsing fails
  }
};

const readCSVFile = (filePath: string): string[] => {
  if (!fs.existsSync(filePath)) {
    console.warn(`File not found: ${filePath}`);
    return [];
  }
  return fs.readFileSync(filePath, "utf-8").split("\n").filter((line) => line.trim() !== "");
};

const migrateLooksData = async (): Promise<number> => {
  const looksFilePath = path.join(process.cwd(), "data", "looks.csv");
  const looksRows = readCSVFile(looksFilePath);
  
  if (looksRows.length === 0) {
    return 0;
  }
  
  const [header, ...rows] = looksRows;
  const columns = header.split(",").map((col) => col.trim());
  
  const looksData = rows.map((row) => {
    const values = row.split(",").map((item) => item.trim());
    const dateIndex = columns.indexOf("Date");
    const otaNameIndex = columns.indexOf("OTAName");
    const successIndex = columns.indexOf("Success");
    const wrongRoutesIndex = columns.indexOf("WrongRoutes");
    const throttledIndex = columns.indexOf("Throttled");
    const failuresIndex = columns.indexOf("Errors");
    
    return {
      date: normalizeDate(values[dateIndex]),
      otaName: values[otaNameIndex],
      success: parseInt(values[successIndex] || "0", 10),
      wrongRoutes: parseInt(values[wrongRoutesIndex] || "0", 10),
      throttled: parseInt(values[throttledIndex] || "0", 10),
      failures: parseInt(values[failuresIndex] || "0", 10)
    };
  });

  // Insert looks data into MongoDB
  if (looksData.length > 0) {
    await Looks.insertMany(looksData);
  }
  return looksData.length;
};

const migrateBooksData = async (): Promise<number> => {
  const booksFilePath = path.join(process.cwd(), "data", "books.csv");
  const booksRows = readCSVFile(booksFilePath);
  
  if (booksRows.length === 0) {
    return 0;
  }
  
  const [header, ...rows] = booksRows;
  const columns = header.split(",").map((col) => col.trim());
  
  const booksData = rows.map((row) => {
    const values = row.split(",").map((item) => item.trim());
    const dateIndex = columns.indexOf("Date");
    const otaNameIndex = columns.indexOf("OTAName");
    const bookingCountsIndex = columns.indexOf("BookingCounts");
    
    return {
      date: normalizeDate(values[dateIndex]),
      otaName: values[otaNameIndex],
      bookingCount: parseInt(values[bookingCountsIndex] || "0", 10)
    };
  });

  // Insert books data into MongoDB
  if (booksData.length > 0) {
    await Books.insertMany(booksData);
  }
  return booksData.length;
};

export async function GET() {
  try {
    // Connect to MongoDB
    await connectDB();
    
    // Clear existing data
    await Looks.deleteMany({});
    await Books.deleteMany({});
    
    // Migrate data
    const looksCount = await migrateLooksData();
    const booksCount = await migrateBooksData();
    
    return NextResponse.json({
      success: true,
      message: "Migration completed successfully",
      data: {
        looksCount,
        booksCount
      }
    });
  } catch (error) {
    console.error("Migration failed:", error);
    return NextResponse.json({ 
      success: false, 
      error: (error as Error).message 
    }, { status: 500 });
  }
} 