import mongoose, { ConnectOptions } from 'mongoose';
import 'dotenv/config';

const MONGODB_URI = process.env.MONGODB_URI;

if (!MONGODB_URI) {
  throw new Error('Please define the MONGODB_URI environment variable inside .env');
}

interface MongooseCache {
  conn: typeof mongoose | null;
  promise: Promise<typeof mongoose> | null;
}

let cached: MongooseCache = { conn: null, promise: null };

async function connectDB() {
  if (cached.conn) {
    return cached.conn;
  }

  if (!cached.promise) {
    cached.promise = mongoose.connect(MONGODB_URI as string).then((mongoose) => {
      return mongoose;
    });
  }

  try {
    cached.conn = await cached.promise;
  } catch (e) {
    cached.promise = null;
    throw e;
  }

  return cached.conn;
}

// Integration Schema
const integrationSchema = new mongoose.Schema({
  batch: { type: String, default: '' },
  masterOtaId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'MasterOTA',
    required: true
  },
  startDate: { type: Date },
  weeks: { type: Number, default: 0 },
  weeksElapsed: { type: Number, default: 0 },
  status: { type: String, default: 'Initiation' },
  lastCommDate: { type: Date },
  expectedEndDate: { type: Date },
  manager: { type: String, default: '' },
  actualEndDate: { type: Date },
  archivedDate: { type: Date },
  archived: { type: Boolean, default: false },
  comments: { type: String }
});

// QuickWin Schema
const quickWinSchema = new mongoose.Schema({
  batch: { type: String, default: '' },
  masterOtaId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'MasterOTA',
    required: true
  },
  startDate: { type: Date },
  weeks: { type: Number, default: 0 },
  weeksElapsed: { type: Number, default: 0 },
  status: { type: String, default: 'Initiation' },
  lastCommDate: { type: Date },
  expectedEndDate: { type: Date },
  manager: { type: String, default: '' },
  actualEndDate: { type: Date },
  archivedDate: { type: Date },
  archived: { type: Boolean, default: false },
  comments: { type: String }
});

// ActivityLog Schema for tracking user actions
const activityLogSchema = new mongoose.Schema({
  action: { type: String, required: true }, // e.g. "Archive Integration", "Create Integration", etc.
  details: { type: String, required: true }, // e.g. "Archived integration: OTA (Batch ID)"
  performedBy: { type: String, required: true }, // User who performed the action
  timestamp: { type: Date, default: Date.now }
}, {
  timestamps: true
});

// Analytics Schema for Looks data
const looksSchema = new mongoose.Schema({
  date: { type: Date, required: true },
  otaName: { type: String, required: true },
  success: { type: Number, required: true },
  wrongRoutes: { type: Number, required: true },
  throttled: { type: Number, required: true },
  failures: { type: Number, required: true }
});

// Add compound index for Looks collection to improve query performance
looksSchema.index({ date: 1, otaName: 1 });

// Analytics Schema for Books data
const booksSchema = new mongoose.Schema({
  date: { type: Date, required: true },
  otaName: { type: String, required: true },
  bookingCount: { type: Number, required: true }
});

// Add compound index for Books collection to improve query performance
booksSchema.index({ date: 1, otaName: 1 });

// SubIntegration Schema
const subIntegrationSchema = new mongoose.Schema({
  batch: { type: String, default: '' },
  masterOtaId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'MasterOTA',
    required: true
  },
  flow: { type: String, default: 'Full Availability' },
  startDate: { type: Date },
  weeks: { type: Number, default: 0 },
  progress: { type: Number, default: 0 },
  status: { type: String, default: 'Active' },
  expectedEndDate: { type: Date },
  manager: { type: String, default: '' },
  actualEndDate: { type: Date },
  archivedDate: { type: Date },
  archived: { type: Boolean, default: false }
});

// Add a pre-find hook to automatically filter out archived documents by default
subIntegrationSchema.pre('find', function() {
  // Only apply this filter if no explicit archived filter was set and no _id is specified
  const query = this.getQuery();
  if (query.archived === undefined && !query._id) {
    this.where({ archived: { $ne: true } });
  }
});

// Add similar hook for countDocuments
subIntegrationSchema.pre('countDocuments', function() {
  const query = this.getQuery();
  if (query.archived === undefined && !query._id) {
    this.where({ archived: { $ne: true } });
  }
});

// Add similar hook for findOne
subIntegrationSchema.pre('findOne', function() {
  const query = this.getQuery();
  if (query.archived === undefined && !query._id) {
    this.where({ archived: { $ne: true } });
  }
});

// LiveIntegration Schema
const liveIntegrationSchema = new mongoose.Schema({
  originalId: { type: String, required: true },
  sourceType: { type: String, enum: ['Integration', 'QuickWin'], required: true },
  batch: { type: String, default: '' },
  ota: { type: String, default: '' },
  masterOtaId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'MasterOTA',
    required: true
  },
  startDate: { type: Date },
  weeks: { type: Number, default: 0 },
  weeksElapsed: { type: Number, default: 0 },
  status: { type: String, default: 'Live' },
  lastCommDate: { type: Date },
  expectedEndDate: { type: Date },
  manager: { type: String, default: '' },
  actualEndDate: { type: Date },
  comments: { type: String },
  archivedDate: { type: Date, default: Date.now },
  metrics: {
    bookings: { type: Number, default: 0 },
    revenue: { type: Number, default: 0 },
    successRate: { type: Number, default: 0 }
  }
}, {
  timestamps: true
});

// Define the integration type sub-schema
const integrationTypeSchema = {
  live: { type: Boolean, default: false },
  liveDate: Date,
  lastUsedDate: { type: Date, default: null }
};

// MasterOTA Schema
const masterOtaSchema = new mongoose.Schema({
  otaName: { type: String, required: true, unique: true },
  type: { type: String, default: '' },
  country: { type: String, default: '' },
  iata: { type: String, default: '' },
  trueIata: { type: String, default: '' },
  fop: { type: String, default: '' },
  clientId: { type: String, default: '' },
  userId: { type: String, default: '' },
  techPartner: { type: String, default: '' }, // Added techPartner field
  contact: { type: String, default: '' },
  // Integration types as nested objects
  booking: integrationTypeSchema,
  ancillaries: integrationTypeSchema,
  seats: integrationTypeSchema,
  cancel: integrationTypeSchema,
  void: integrationTypeSchema,
  change: integrationTypeSchema,
  interline: integrationTypeSchema,
  multiCity: integrationTypeSchema,
  apis: integrationTypeSchema
}, {
  timestamps: true
});

// Usage Schema
const usageSchema = new mongoose.Schema({
  masterOtaId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'MasterOTA',
    required: true
  },
  date: { type: Date, required: true },
  metrics: {
    searches: { type: Number, default: 0 },
    bookings: { type: Number, default: 0 }
  }
});

// Create models
const Integration = mongoose.models.Integration || mongoose.model('Integration', integrationSchema);
const QuickWin = mongoose.models.QuickWin || mongoose.model('QuickWin', quickWinSchema);
const Looks = mongoose.models.Looks || mongoose.model('Looks', looksSchema);
const Books = mongoose.models.Books || mongoose.model('Books', booksSchema);
const SubIntegration = mongoose.models.SubIntegration || mongoose.model('SubIntegration', subIntegrationSchema);
const LiveIntegration = mongoose.models.LiveIntegration || mongoose.model('LiveIntegration', liveIntegrationSchema);
const ActivityLog = mongoose.models.ActivityLog || mongoose.model('ActivityLog', activityLogSchema);
const MasterOTA = mongoose.models.MasterOTA || mongoose.model('MasterOTA', masterOtaSchema);
const Usage = mongoose.models.Usage || mongoose.model('Usage', usageSchema);

// Export all models
export { connectDB, Integration, QuickWin, Looks, Books, SubIntegration, LiveIntegration, ActivityLog, MasterOTA, Usage };