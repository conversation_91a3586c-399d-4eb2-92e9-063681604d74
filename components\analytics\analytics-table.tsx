import React from "react";
import { Download } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { TablePagination } from "@/components/ui/table-pagination";
import { formatDate } from "@/lib/date-utils";

interface AnalyticsTableProps {
  date: string;
  columns: string[];
  data: { [key: string]: string | number | Date }[];
  currentPage: number;
  totalPages: number;
  onPageChange: (newPage: number) => void;
  searchTerm: string;
  columnNameMapping?: { [key: string]: string };
}


const AnalyticsTable: React.FC<AnalyticsTableProps> = ({
  columns,
  data = [],
  currentPage,
  date,
  totalPages,
  onPageChange,
  searchTerm,
  columnNameMapping = {WrongRoutes: "Non-Operating Routes", Failures: "Failures"},
}) => {  // Pagination is now handled by the TablePagination component

  return (
    <div className="space-y-4">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              {columns.map((column) => (
                <TableHead key={column} className="font-semibold">
                  {columnNameMapping[column] || column}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.map((row, rowIndex) => (
              <TableRow
                key={rowIndex}
                className="hover:bg-muted/20"
              >
                {columns.map((column) => (
                  <TableCell key={column}>
                    {column === 'Date' || column === 'date' ? 
                      formatDate(row[column] as string | Date) : 
                      String(row[column] || "-")}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>      {data.length > 0 && (
        <TablePagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={onPageChange}
          maxVisiblePages={3}
          className="justify-end"
        />
      )}
    </div>
  );
};

export default AnalyticsTable;
