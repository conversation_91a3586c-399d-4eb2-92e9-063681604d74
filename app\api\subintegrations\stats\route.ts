import { NextRequest, NextResponse } from 'next/server';
import { connectDB, SubIntegration } from '@/lib/mongodb';
import { SubIntegrationStatus } from '@/types';

export async function GET(request: NextRequest) {
  try {
    await connectDB();
    
    // Count total sub-integrations
    const total = await SubIntegration.countDocuments();
    
    // Count sub-integrations by status
    const byStatus: Record<SubIntegrationStatus, number> = {
        Initiation: 0,
        Integration: 0,
        Testing: 0,
        Demo: 0,
        Live: 0,
        Hold: 0,
    };
  
    // Get all sub-integrations for calculations
    const subIntegrations = await SubIntegration.find();
    
    // Count by status and calculate other metrics
    let completedCount = 0;
    let totalCompletionTime = 0;
    let delayedIntegrations = 0;
    
    subIntegrations.forEach(integration => {
      // Count by status
      if (byStatus.hasOwnProperty(integration.status)) {
        byStatus[integration.status as SubIntegrationStatus]++;
      }
      
      // Calculate completion time for completed integrations
      if (integration.status === 'Completed' && integration.actualEndDate) {
        completedCount++;
        const startDate = new Date(integration.startDate);
        const endDate = new Date(integration.actualEndDate);
        const weeksDifference = (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24 * 7);
        totalCompletionTime += weeksDifference;
      }
      
      // Count delayed integrations
      if (integration.status !== 'Completed' && integration.expectedEndDate) {
        const currentDate = new Date();
        const expectedEndDate = new Date(integration.expectedEndDate);
        if (currentDate > expectedEndDate) {
          delayedIntegrations++;
        }
      }
    });
    
    // Calculate average completion time
    const averageCompletionTime = completedCount > 0 ? totalCompletionTime / completedCount : 0;
    
    return NextResponse.json({
      total,
      byStatus,
      averageCompletionTime,
      delayedIntegrations,
    });
  } catch (error) {
    console.error('Error fetching sub-integration stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch sub-integration stats' },
      { status: 500 }
    );
  }
} 