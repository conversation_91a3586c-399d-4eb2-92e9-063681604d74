import { format } from "date-fns";

/**
 * Formats a date into a standardized string format
 * @param date - Date object, string, or undefined/null to format
 * @param formatStr - Optional format string (default is "dd-MMM-yy")
 * @returns Formatted date string or "N/A" if invalid/null/undefined
 */
export const formatDate = (date: Date | string | undefined | null, formatStr: string = "dd-MMM-yy"): string => {
  if (!date) return "N/A";
  
  try {
    const d = new Date(date);
    if (isNaN(d.getTime())) return "Invalid Date";
    
    // Use date-fns format function with the provided format string
    return format(d, formatStr);
  } catch (error) {
    return "Invalid Date";
  }
};

/**
 * Parses a date string in various formats into a Date object
 * @param dateStr - Date string to parse
 * @returns Date object or current date as fallback
 */
export const parseDate = (dateStr: string | Date): Date => {
  let dateObj;
  try {
    // Try to parse the date string
    if (typeof dateStr === 'string') {
      const parts = dateStr.split(/[-\/]/);

      // If format is like 01-Apr-25, parse it specially
      if (parts.length === 3 && isNaN(parseInt(parts[1]))) {
        const monthMap: Record<string, number> = {
          'jan': 0, 'feb': 1, 'mar': 2, 'apr': 3, 'may': 4, 'jun': 5,
          'jul': 6, 'aug': 7, 'sep': 8, 'oct': 9, 'nov': 10, 'dec': 11
        };

        const day = parseInt(parts[0]);
        const month = monthMap[parts[1].toLowerCase().substring(0, 3)] || 0;
        const year = parseInt('20' + parts[2]); // Assume 20xx for two-digit years

        dateObj = new Date(year, month, day);
      } else {
        // Standard date parsing
        dateObj = new Date(dateStr);
      }
    } else if (dateStr instanceof Date) {
      dateObj = dateStr;
    } else {
      dateObj = new Date();
    }

    // If date parsing fails, use a fallback
    if (isNaN(dateObj.getTime())) {
      console.warn(`Invalid date: ${dateStr}`);
      dateObj = new Date(); // Use current date as fallback
    }
  } catch (error) {
    console.error(`Error parsing date: ${dateStr}`, error);
    dateObj = new Date(); // Use current date as fallback
  }

  return dateObj;
};

/**
 * Formats a date for chart display
 * @param dateStr - Date string to format
 * @returns Formatted date string (Month Day)
 */
export const formatDateForChart = (dateStr: string): string => {
  const date = new Date(dateStr);
  return date.toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
  });
};

/**
 * Formats a date with year for tooltip display
 * @param dateStr - Date string to format
 * @returns Formatted date string (Month Day, Year)
 */
export const formatDateWithYear = (dateStr: string): string => {
  const date = new Date(dateStr);
  return date.toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
    year: "numeric",
  });
};

/**
 * Gets the current date in ISO format (YYYY-MM-DD)
 * @returns Current date in ISO format
 */
export const getCurrentDateISO = (): string => {
  return new Date().toISOString().split('T')[0];
};

/**
 * Calculates the number of weeks elapsed between two dates
 * @param startDate - Start date
 * @param actualEndDate - End date (default: current date)
 * @returns Number of weeks elapsed
 */
export const calculateWeeksElapsed = (startDate: Date | string, actualEndDate?: Date | string | null): number => {
  const start = new Date(startDate);
  const end = actualEndDate ? new Date(actualEndDate) : new Date();
  const timeDifference = end.getTime() - start.getTime();
  return Math.max(0, Math.round((timeDifference / (1000 * 60 * 60 * 24 * 7)) * 10) / 10);
};

/**
 * Determines if an integration is delayed based on its expected vs actual duration
 * @param integration - Integration object with weeks and weeksElapsed properties
 * @returns Boolean indicating if the integration is delayed
 */
export const isIntegrationDelayed = (integration: { weeks: number; weeksElapsed: number }): boolean => {
  const expectedWeeks = integration.weeks;
  const elapsedWeeks = integration.weeksElapsed;
  return elapsedWeeks > expectedWeeks;
};
