"use client";

import { SubIntegration, SubIntegrationFlow, SubIntegrationStatus, subIntegrationFlows, subIntegrationStatuses } from "@/types";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { getIntegrationStatusBgColor } from "@/lib/format-utils";
import { 
  formatDate as formatDateUtil, 
  calculateWeeksElapsed,
  isIntegrationDelayed
} from "@/lib/date-utils";
import { TablePagination } from "@/components/ui/table-pagination";
import { useCallback, useState, useEffect, useMemo } from "react";
import useSWR from 'swr';
import { EditSubIntegrationDialog } from "@/components/subintegrations/edit-subintegration-dialog";
import { Input } from "@/components/ui/input";
import { Search, Filter, Download } from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface SubIntegrationsTableProps {
  subintegrations: SubIntegration[]; 
  currentPage: number;
  onPageChange: (page: number) => void;
  onFilteredDataChange?: (data: SubIntegration[]) => void;
}

interface Filters {
  batch?: string;
  flow?: SubIntegrationFlow;
  status?: SubIntegrationStatus;
  country?: string;
  manager?: string;
}

const fetcher = (url: string) => fetch(url).then(res => res.json());

const ALL_VALUE = "all";

// Use the utility function for status colors
const getStatusColor = getIntegrationStatusBgColor;

const handleExport = async () => {
  try {
    const response = await fetch('/api/subintegrations/export');
    if (!response.ok) throw new Error('Export failed');
    
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'subintegrations.xlsx';
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  } catch (error) {
    console.error('Error exporting subintegrations:', error);
  }
};

// Using the utility function for calculating weeks elapsed

export function SubIntegrationsTable({ 
  currentPage, 
  onPageChange,
  onFilteredDataChange,
  subintegrations
}: SubIntegrationsTableProps) {
  const [selectedSubIntegration, setSelectedSubIntegration] = useState<SubIntegration | null>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeSearch, setActiveSearch] = useState('');
  const [filters, setFilters] = useState<Filters>({});
  const pageSize = 25;
  
  const { data: swrData, error, isLoading } = useSWR<{
    data: SubIntegration[];
    total: number;
    totalPages: number;
  }>(
    `/api/subintegrations?page=${currentPage}&pageSize=${pageSize}&search=${encodeURIComponent(activeSearch)}`,
    fetcher,
    {
      keepPreviousData: true
    }
  );
  const totalPages = swrData?.totalPages || 0;

  const handleSearch = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      setActiveSearch(searchTerm);
      onPageChange(1);
    }
  };

  const handleRowClick = (subintegration: SubIntegration) => {
    setSelectedSubIntegration(subintegration);
    setEditDialogOpen(true);
  };

  // This implementation is different from the standard isIntegrationDelayed 
  // because it checks expectedEndDate instead of weeks
  const isDelayed = (subintegration: SubIntegration) => {
    if (subintegration.status === 'Live' || !subintegration.expectedEndDate) return false;
    const currentDate = new Date();
    const expectedEndDate = new Date(subintegration.expectedEndDate);
    return currentDate > expectedEndDate;
  };

  // Use the utility function for date formatting
  const formatDate = formatDateUtil;

  const handleFilterChange = (key: keyof Filters, value: string | null) => {
    setFilters(prev => {
      const newFilters = {
        ...prev,
        [key]: value === ALL_VALUE ? undefined : value
      };
      return newFilters;
    });
    onPageChange(1);
  };

  const filteredData = useMemo(() => {
    if (!swrData?.data) return [];
    return swrData.data.filter((subintegration: SubIntegration) => {
      if (filters.batch && subintegration.batch !== filters.batch) return false;
      if (filters.flow && subintegration.flow !== filters.flow) return false;
      if (filters.status && subintegration.status !== filters.status) return false;
      if (filters.country && subintegration.country !== filters.country) return false;
      if (filters.manager && subintegration.manager !== filters.manager) return false;
      return true;
    });
  }, [swrData?.data, filters]);

  useEffect(() => {
    if (onFilteredDataChange) {
      onFilteredDataChange(filteredData);
    }
  }, [filteredData, onFilteredDataChange]);

  const handlePageChange = useCallback((newPage: number) => {
    onPageChange(newPage);
  }, [onPageChange]);

  const uniqueBatches = useMemo<string[]>(() => 
    Array.from(new Set((swrData?.data?.map((i: SubIntegration) => i.batch) || []) as string[])).sort(),
    [swrData?.data]
  );

  const uniqueCountries = useMemo<string[]>(() => 
    Array.from(new Set((swrData?.data?.map((i: SubIntegration) => i.country) || []) as string[])).sort(),
    [swrData?.data]
  );

  const uniqueManagers = useMemo<string[]>(() => 
    Array.from(new Set((swrData?.data?.map((i: SubIntegration) => i.manager) || []) as string[])).sort(),
    [swrData?.data]
  );

  // Pagination is now handled by the TablePagination component

  if (error) {
    return <div className="text-center p-4">Error loading data. Please try again later.</div>;
  }

  if (isLoading && !swrData) {
    return <div className="text-center p-4">Loading data...</div>;
  }

  return (
    <div>
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4">
        <div className="relative w-full sm:w-auto flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search by OTA, country, manager, IATA, batch..."
            className="pl-10 w-full"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onKeyDown={handleSearch}
          />
        </div>
        <div className="flex gap-2 flex-wrap">
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" size="sm" className="flex items-center gap-1">
                <Filter className="h-4 w-4" /> Filter
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-72 p-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="batch-filter">Batch</Label>
                  <Select
                    onValueChange={(value) => handleFilterChange("batch", value)}
                    value={filters.batch || ALL_VALUE}
                  >
                    <SelectTrigger id="batch-filter">
                      <SelectValue placeholder="Select batch" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={ALL_VALUE}>All Batches</SelectItem>
                      {uniqueBatches.map((batch) => (
                        <SelectItem key={batch} value={batch}>
                          {batch}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="flow-filter">Flow</Label>
                  <Select
                    onValueChange={(value) => handleFilterChange("flow", value)}
                    value={filters.flow || ALL_VALUE}
                  >
                    <SelectTrigger id="flow-filter">
                      <SelectValue placeholder="Select flow" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={ALL_VALUE}>All Flows</SelectItem>
                      {subIntegrationFlows.map((flow) => (
                        <SelectItem key={flow} value={flow}>
                          {flow}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="status-filter">Status</Label>
                  <Select
                    onValueChange={(value) => handleFilterChange("status", value)}
                    value={filters.status || ALL_VALUE}
                  >
                    <SelectTrigger id="status-filter">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={ALL_VALUE}>All Statuses</SelectItem>
                      {subIntegrationStatuses.map((status) => (
                        <SelectItem key={status} value={status}>
                          {status}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="country-filter">Country</Label>
                  <Select
                    onValueChange={(value) => handleFilterChange("country", value)}
                    value={filters.country || ALL_VALUE}
                  >
                    <SelectTrigger id="country-filter">
                      <SelectValue placeholder="Select country" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={ALL_VALUE}>All Countries</SelectItem>
                      {uniqueCountries.map((country) => (
                        <SelectItem key={country} value={country}>
                          {country}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="manager-filter">Manager</Label>
                  <Select
                    onValueChange={(value) => handleFilterChange("manager", value)}
                    value={filters.manager || ALL_VALUE}
                  >
                    <SelectTrigger id="manager-filter">
                      <SelectValue placeholder="Select manager" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={ALL_VALUE}>All Managers</SelectItem>
                      {uniqueManagers.map((manager) => (
                        <SelectItem key={manager} value={manager}>
                          {manager}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </PopoverContent>
          </Popover>
          <Button onClick={handleExport} variant="outline" size="sm" className="flex items-center gap-1">
            <Download className="h-4 w-4" /> Export
          </Button>
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="font-semibold">Batch</TableHead>
              <TableHead className="font-semibold">OTA Name</TableHead>
              <TableHead className="font-semibold">Country</TableHead>
              <TableHead className="font-semibold">Flow</TableHead>
              <TableHead className="font-semibold">IATA</TableHead>
              <TableHead className="font-semibold">Start Date</TableHead>
              <TableHead className="font-semibold">Progress</TableHead>
              <TableHead className="font-semibold text-center">Status</TableHead>
              <TableHead className="font-semibold">Expected End</TableHead>
              <TableHead className="font-semibold">Manager</TableHead>
              <TableHead className="font-semibold">Actual End</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredData.length > 0 ? (
              filteredData.map((subintegration) => {
                const weeksElapsed = calculateWeeksElapsed(subintegration.startDate, subintegration.actualEndDate);
                return (
                  <TableRow 
                    key={subintegration._id}
                    className={`cursor-pointer hover:bg-muted ${isDelayed(subintegration) ? 'bg-red-50 dark:bg-red-950/10' : ''}`}
                    onClick={() => handleRowClick(subintegration)}
                  >
                    <TableCell className="font-medium">{subintegration.batch}</TableCell>
                    <TableCell>{subintegration.ota}</TableCell>
                    <TableCell>{subintegration.country}</TableCell>
                    <TableCell>{subintegration.flow}</TableCell>
                    <TableCell>{subintegration.iata}</TableCell>
                    <TableCell>{formatDate(subintegration.startDate)}</TableCell>
                    <TableCell>{weeksElapsed} of {subintegration.weeks} weeks</TableCell>
                    <TableCell className="text-center">
                      <Badge className={getStatusColor(subintegration.status)}>
                        {subintegration.status}
                      </Badge>
                    </TableCell>
                    <TableCell>{formatDate(subintegration.expectedEndDate)}</TableCell>
                    <TableCell>{subintegration.manager}</TableCell>
                    <TableCell>{formatDate(subintegration.actualEndDate)}</TableCell>
                  </TableRow>
                );
              })
            ) : (
              <TableRow>
                <TableCell colSpan={11} className="h-32 text-center">
                  {isLoading ? 'Loading...' : 'No results found.'}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {filteredData.length > 0 && (
        <TablePagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
          maxVisiblePages={3}
          totalItems={filteredData.length}
          pageSize={pageSize}
        />
      )}

      {selectedSubIntegration && (
        <EditSubIntegrationDialog
          subintegration={selectedSubIntegration}
          open={editDialogOpen}
          onOpenChange={setEditDialogOpen}
        />
      )}
    </div>
  );
}