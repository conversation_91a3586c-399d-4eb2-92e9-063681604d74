"use client";

import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { IntegrationStatus } from "@/types";
import { getIntegrationStatusColor, formatPercentage } from "@/lib/format-utils";

interface StatusCardProps {
  status: IntegrationStatus;
  count: number;
  total: number;
}

export function StatusCard({ status, count, total }: StatusCardProps) {
  const percentageValue = (count / total) * 100;
  const statusColor = getIntegrationStatusColor(status.toLowerCase());

  return (
    <Card className="shadow-sm hover:shadow-md transition-shadow">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className={`text-2xl font-bold ${statusColor}`}>{status}</CardTitle>
        <div className={`text-2xl font-bold ${statusColor}`}>{count}</div>
      </CardHeader>
      <CardContent>
        <div className="text-xs text-muted-foreground">
          {formatPercentage(percentageValue)} of total integrations
        </div>
      </CardContent>
    </Card>
  );
}