'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { logout } from '../lib/auth';
import { LogOut, PlusCircle } from 'lucide-react';
import { ModeToggle } from '@/components/mode-toggle';
import { NewMasterOtaDialog } from '@/components/master-otas/new-master-ota-dialog';

export function Header() {
  const pathname = usePathname();

  const handleLogout = async () => {
    try {
      document.cookie = "id_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 UTC; secure; SameSite=Strict";
      await logout();
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };


  return (
    <header
      className="sticky top-0 border-b z-50 bg-[#0851a7] dark:bg-zinc-900"
    >
      <div className="px-8 py-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-8">
            <Link href="/">
              <Image
                src="https://www.flydubai.com/v2/_next/static/media/iconF.661174c2.svg"
                alt="flydubai logo"
                width={160}
                height={60}
                priority
                className="cursor-pointer"
              />
            </Link>
            <nav className="flex items-center space-x-6">
              <Link 
                href="/" 
                className={cn(
                  "text-white hover:text-gray-200 text-lg font-medium transition-colors",
                  pathname === "/" && "border-b-2 border-white"
                )}
              >
                Home
              </Link>
              <Link 
                href="/quickwins" 
                className={cn(
                  "text-white hover:text-gray-200 text-lg font-medium transition-colors",
                  pathname === "/quickwins" && "border-b-2 border-white"
                )}
              >
                Quick Wins
              </Link>
              <Link 
                href="/subintegrations" 
                className={cn(
                  "text-white hover:text-gray-200 text-lg font-medium transition-colors",
                  pathname === "/subintegrations" && "border-b-2 border-white"
                )}
              >
                Sub-Integrations
              </Link>
              <Link 
                href="/usages" 
                className={cn(
                  "text-white hover:text-gray-200 text-lg font-medium transition-colors",
                  pathname === "/usages" && "border-b-2 border-white"
                )}
              >
                Modules
              </Link>
              <Link 
                href="/analytics" 
                className={cn(
                  "text-white hover:text-gray-200 text-lg font-medium transition-colors",
                  pathname === "/analytics" && "border-b-2 border-white"
                )}
              >
                Analytics
              </Link>
              <Link 
                href="/reports" 
                className={cn(
                  "text-white hover:text-gray-200 text-lg font-medium transition-colors",
                  pathname === "/reports" && "border-b-2 border-white"
                )}
              >
                Reports
              </Link>
              <Link 
                href="/master-otas" 
                className={cn(
                  "text-white hover:text-gray-200 text-lg font-medium transition-colors",
                  pathname === "/master-otas" && "border-b-2 border-white"
                )}
              >
                Hub
              </Link>      
            </nav>
          </div>
          <div className="flex items-center space-x-2">
            <div className="mr-2">
              <NewMasterOtaDialog>
                <Button 
                  variant="outline" 
                  size="icon"
                >
                  <PlusCircle className="h-[1.2rem] w-[1.2rem]" />
                </Button>
              </NewMasterOtaDialog>
            </div>
            <Button
              variant="outline"
              size="icon"
              onClick={handleLogout}
            >
              <LogOut className="h-[1.2rem] w-[1.2rem]" />
            </Button>
            <ModeToggle />
          </div>
        </div>
      </div>
    </header>
  );
}