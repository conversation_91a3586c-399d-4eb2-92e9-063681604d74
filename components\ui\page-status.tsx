'use client';

import { Header } from '@/components/header';

interface PageStatusProps {
  isLoading?: boolean;
  error?: Error | null;
}

export function PageStatus({ isLoading, error }: PageStatusProps) {
  if (error) {
    return (
      <div className="min-h-screen">
        <Header />
        <div className="px-8 py-10">Error loading dashboard data. Please try again later.</div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="min-h-screen">
        <Header />
        <div className="px-8 py-10">Loading...</div>
      </div>
    );
  }

  return null;
}
