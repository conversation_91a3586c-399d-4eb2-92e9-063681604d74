'use client';

import { useState } from 'react';
import { Head<PERSON> } from "@/components/header";
import { PageStatus } from "@/components/ui/page-status";
import { DashboardHeader } from "@/components/quickwins/dashboard-header";
import { StatsCardsGrid } from "@/components/quickwins/stats-cards-grid";
import { IntegrationProgress } from "@/components/quickwins/integration-progress";
import { useQuickWinStats } from "@/hooks/use-quickwin-stats";

export default function QuickWins() {
  const [page, setPage] = useState(1);

  // Use the custom hook for stats management
  const {
    statsError,
    filteredData,
    setFilteredData,
    displayStats,
    isLoading
  } = useQuickWinStats();

  // Show loading or error states
  if (statsError || isLoading) {
    return <PageStatus isLoading={isLoading} error={statsError} />;
  }

  return (
    <div className="min-h-screen">
      <Header />
      <div className="px-4 py-6">
        <DashboardHeader displayStats={displayStats}>
          <StatsCardsGrid displayStats={displayStats} />
        </DashboardHeader>

        <IntegrationProgress
          displayStats={displayStats}
          page={page}
          setPage={setPage}
          filteredData={filteredData}
          setFilteredData={setFilteredData}
        />
      </div>
    </div>
  );
}
