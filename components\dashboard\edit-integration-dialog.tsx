"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>er,
  Drawer<PERSON>lose,
  <PERSON>er<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>erFooter,
} from "@/components/ui/drawer";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useEffect, useState } from "react";
import { Integration, IntegrationStatus, IntegrationType, integrationTypes, integrationStatuses } from "@/types";
import { mutate } from "swr";
import { Trash2, Calendar as CalendarIcon, Check, ChevronsUpDown } from "lucide-react";
import { format } from "date-fns";
import { Pop<PERSON>, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";
import mongoose from "mongoose";
import { NextResponse } from "next/server";

interface MasterOTA {
  _id: string;
  otaName: string;
  type: string;
  country: string;
  iata: string;
  trueIata: string;
  fop: string;
  clientId: string;
  userId: string;
  contact: string;
}

interface EditIntegrationDialogProps {
  integration: Integration | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function EditIntegrationDialog({ integration, open, onOpenChange }: EditIntegrationDialogProps) {
  const [formData, setFormData] = useState<Partial<Integration>>({});
  const [isDeleting, setIsDeleting] = useState(false);
  const [isArchiving, setIsArchiving] = useState(false);
  const [showDeleteAlert, setShowDeleteAlert] = useState(false);
  const [showArchiveAlert, setShowArchiveAlert] = useState(false);
  const [masterOtas, setMasterOtas] = useState<MasterOTA[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedMasterOtaId, setSelectedMasterOtaId] = useState<string>('');
  const [otaSearchOpen, setOtaSearchOpen] = useState(false);
  const [errors, setErrors] = useState<{ 
    actualEndDate?: string;
    clientId?: string;
    userId?: string;
  }>({}); // Update error state

  // Fetch master OTAs when the dialog opens
  useEffect(() => {
    if (open) {
      fetchMasterOtas();
    }
  }, [open]);

  const fetchMasterOtas = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/master-otas');
      if (response.ok) {
        const data = await response.json();
        setMasterOtas(data);
      } else {
        console.error('Failed to fetch master OTAs');
      }
    } catch (error) {
      console.error('Error fetching master OTAs:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (integration) {
      setFormData({
        batch: integration.batch,
        ota: integration.ota,
        type: integration.type,
        country: integration.country,
        fop: integration.fop,
        iata: integration.iata,
        trueIata: integration.trueIata,
        weeks: integration.weeks,
        manager: integration.manager,
        status: integration.status,
        weeksElapsed: integration.weeksElapsed,
        startDate: integration.startDate ? new Date(integration.startDate) : undefined,
        lastCommDate: integration.lastCommDate ? new Date(integration.lastCommDate) : undefined,
        actualEndDate: integration.actualEndDate ? new Date(integration.actualEndDate) : undefined,
        comments: integration.comments || '',
        contact: integration.contact || '',
        clientId: integration.clientId || '',
        userId: integration.userId || '',
      });
      setSelectedMasterOtaId(integration.masterOtaId?.toString() || '');
    }
  }, [integration]);

  const handleOtaSelection = (otaId: string) => {
    setSelectedMasterOtaId(otaId);
    const selectedOta = masterOtas.find(ota => ota._id === otaId);
    if (selectedOta) {
      setFormData(prevData => ({
        ...prevData,
        ota: selectedOta.otaName?.toString() || '',
        type: selectedOta.type,
        country: selectedOta.country,
        fop: selectedOta.fop,
        iata: selectedOta.iata,
        trueIata: selectedOta.trueIata,
        clientId: selectedOta.clientId,
        userId: selectedOta.userId
      }));
    }
    setOtaSearchOpen(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!integration) return;
    
    // Validate required fields based on status
    const errors: Record<string, string> = {};
    if (formData.status === 'Live') {
      if (!formData.clientId) errors.clientId = 'Client ID is required for live integrations';
      if (!formData.userId) errors.userId = 'User ID is required for live integrations';
    }

    if (Object.keys(errors).length > 0) {
      setErrors(errors);
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`/api/integrations/${integration._id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        onOpenChange(false);
        // Refresh all integration-related data
        await Promise.all([
          mutate((key) => typeof key === 'string' && key.startsWith('/api/integrations')),
          mutate('/api/stats')
        ]);
      } else {
        console.error('Failed to update integration');
      }
    } catch (error) {
      console.error('Error updating integration:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!integration || !integration._id) {
      console.error('Cannot delete: Invalid integration or missing ID');
      setShowDeleteAlert(false);
      onOpenChange(false);
      return;
    }

    setIsDeleting(true);
    try {
      const response = await fetch(`/api/integrations/${integration._id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setShowDeleteAlert(false);
        onOpenChange(false);
        // Refresh all integration-related data
        await Promise.all([
          mutate((key) => typeof key === 'string' && key.startsWith('/api/integrations')),
          mutate('/api/stats')
        ]);
      } else {
        const errorData = await response.json();
        console.error('Failed to delete integration:', errorData.error);
      }
    } catch (error) {
      console.error('Error deleting integration:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleArchive = async () => {
    if (!integration || !integration._id) {
      console.error('Cannot archive: Invalid integration or missing ID');
      setShowArchiveAlert(false);
      onOpenChange(false);
      return;
    }

    setIsArchiving(true);
    try {
      // 1. Archive the integration using the dedicated archive endpoint
      console.log('Attempting to archive integration with ID:', integration._id);
      
      // Use the dedicated archive endpoint
      const archiveResponse = await fetch(`/api/integrations/${integration._id}/archive`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      const archiveResult = await archiveResponse.json();
      
      if (!archiveResponse.ok || !archiveResult.success) {
        console.error('Failed to archive integration:', archiveResult);
        setIsArchiving(false);
        return;
      }

      console.log('Archive response:', archiveResult);

      // 2. Update master OTA booking status and live date
      if (selectedMasterOtaId || integration.masterOtaId) {
        const masterOtaId = (selectedMasterOtaId || integration.masterOtaId).toString();
        const masterOtaResponse = await fetch(`/api/master-otas/${masterOtaId}/update-booking`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            integrationType: integration.type?.toLowerCase() || 'booking'
          })
        });

        if (!masterOtaResponse.ok) {
          console.error('Failed to update master OTA integration status');
        }
      }

      // 3. Log the activity
      const activityData = {
        action: 'Archive Integration',
        details: `Archived integration: ${integration.ota} (${integration.batch})`,
        performedBy: integration.manager || 'Unknown',
        timestamp: new Date()
      };

      const activityResponse = await fetch('/api/activity-logs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(activityData),
      });

      if (!activityResponse.ok) {
        console.error('Failed to log activity');
      }

      // 4. Close dialogs and refresh data
      setShowArchiveAlert(false);
      onOpenChange(false);
      
      // Refresh all integration-related data
      await Promise.all([
        mutate((key) => typeof key === 'string' && key.startsWith('/api/integrations')),
        mutate('/api/stats'),
        mutate('/api/activity-logs'),
        mutate('/api/master-otas')
      ]);
    } catch (error) {
      console.error('Error during archive process:', error);
    } finally {
      setIsArchiving(false);
    }
  };

  if (!integration) return null;

  return (
    <>
      <Drawer open={open} onOpenChange={onOpenChange}>
        <DrawerContent>
          <div className="mx-auto w-full max-w-4xl px-6">
            <DrawerHeader>
              <DrawerTitle className="text-2xl font-semibold">Edit Integration</DrawerTitle>
            </DrawerHeader>
            <form onSubmit={handleSubmit} className="py-4">
              {/* Basic Information */}
              <div className="mb-5">
                <h3 className="text-base font-semibold mb-2 border-b pb-1">Basic Information</h3>
                <div className="grid grid-cols-4 gap-x-4 gap-y-3">
                  <div className="space-y-1">
                    <Label htmlFor="batch" className="text-sm">Batch ID</Label>
                    <Input
                      id="batch"
                      value={formData.batch || ''}
                      onChange={(e) => setFormData({ ...formData, batch: e.target.value })}
                      required
                      className="h-8"
                    />
                  </div>
                  <div className="space-y-1">
                    <Label htmlFor="otaSelect" className="text-sm">Select OTA</Label>
                    <Popover open={otaSearchOpen} onOpenChange={setOtaSearchOpen}>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          role="combobox"
                          aria-expanded={otaSearchOpen}
                          className="h-8 w-full justify-between"
                        >
                          {selectedMasterOtaId
                            ? masterOtas.find((ota) => ota._id === selectedMasterOtaId)?.otaName || formData.ota
                            : "Select OTA"}
                          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-full p-0">
                        <Command>
                          <CommandInput placeholder="Search OTA..." className="h-9" />
                          <CommandEmpty>No OTA found.</CommandEmpty>
                          <CommandList>
                            <CommandGroup>
                              {loading ? (
                                <CommandItem disabled>Loading...</CommandItem>
                              ) : masterOtas.length > 0 ? (
                                masterOtas.map((ota) => (
                                  <CommandItem
                                    key={ota._id}
                                    value={ota.otaName}
                                    onSelect={() => handleOtaSelection(ota._id)}
                                  >
                                    <Check
                                      className={cn(
                                        "mr-2 h-4 w-4",
                                        selectedMasterOtaId === ota._id ? "opacity-100" : "opacity-0"
                                      )}
                                    />
                                    {ota.otaName} {ota.country ? `(${ota.country})` : ''}
                                  </CommandItem>
                                ))
                              ) : (
                                <CommandItem disabled>No OTAs found</CommandItem>
                              )}
                            </CommandGroup>
                          </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>
                  </div>
                  <div className="space-y-1">
                    <Label htmlFor="type" className="text-sm">Integration Type</Label>
                    <Input
                      id="type"
                      value={formData.type || ''}
                      readOnly
                      disabled
                      className="h-8 bg-muted"
                    />
                  </div>
                  <div className="space-y-1">
                    <Label htmlFor="country" className="text-sm">Country</Label>
                    <Input
                      id="country"
                      value={formData.country || ''}
                      readOnly
                      disabled
                      className="h-8 bg-muted"
                    />
                  </div>
                  <div className="space-y-1">
                    <Label htmlFor="fop" className="text-sm">Form of Payment</Label>
                    <Input
                      id="fop"
                      value={formData.fop || ''}
                      readOnly
                      disabled
                      className="h-8 bg-muted"
                    />
                  </div>
                  <div className="space-y-1">
                    <Label htmlFor="manager" className="text-sm">Project Manager</Label>
                    <Input
                      id="manager"
                      value={formData.manager || ''}
                      onChange={(e) => setFormData({ ...formData, manager: e.target.value })}
                      required
                      className="h-8"
                    />
                  </div>
                </div>
              </div>

              {/* Identification & Access */}
              <div className="mb-5">
                <h3 className="text-base font-semibold mb-2 border-b pb-1">Identification & Access</h3>
                <div className="grid grid-cols-4 gap-x-4 gap-y-3">
                  <div className="space-y-1">
                    <Label htmlFor="iata" className="text-sm">IATA Code</Label>
                    <Input
                      id="iata"
                      value={formData.iata || ''}
                      readOnly
                      disabled
                      className="h-8 bg-muted"
                    />
                  </div>
                  <div className="space-y-1">
                    <Label htmlFor="trueIata" className="text-sm">True IATA</Label>
                    <Input
                      id="trueIata"
                      value={formData.trueIata || ''}
                      readOnly
                      disabled
                      className="h-8 bg-muted"
                    />
                  </div>
                  <div className="space-y-1">
                    <Label htmlFor="clientId" className="text-sm flex items-center gap-1">
                      Client ID
                      {formData.status === 'Live' && <span className="text-purple-500">*</span>}
                    </Label>
                    <Input
                      id="clientId"
                      value={formData.clientId || ''}
                      readOnly
                      disabled
                      className={cn("h-8 bg-muted", errors.clientId && "border-purple-500")}
                    />
                    {errors.clientId && <p className="text-purple-500 text-xs">{errors.clientId}</p>}
                  </div>
                  <div className="space-y-1">
                    <Label htmlFor="userId" className="text-sm flex items-center gap-1">
                      User ID
                      {formData.status === 'Live' && <span className="text-purple-500">*</span>}
                    </Label>
                    <Input
                      id="userId"
                      value={formData.userId || ''}
                      readOnly
                      disabled
                      className={cn("h-8 bg-muted", errors.userId && "border-purple-500")}
                    />
                    {errors.userId && <p className="text-purple-500 text-xs">{errors.userId}</p>}
                  </div>
                </div>
              </div>

              {/* Timeline & Status */}
              <div className="mb-5">
                <h3 className="text-base font-semibold mb-2 border-b pb-1">Timeline & Status</h3>
                <div className="grid grid-cols-12 gap-x-3 gap-y-3">
                  <div className="col-span-2 space-y-1">
                    <Label htmlFor="status" className="text-sm">Status</Label>
                    <Select
                      value={formData.status || ''}
                      onValueChange={(value) => {
                        setFormData({ ...formData, status: value as IntegrationStatus });
                        // Ensure actualEndDate is cleared if status is not Live
                        if (value !== 'Live') {
                          setFormData((prev) => ({ ...prev, actualEndDate: undefined }));
                        }
                      }}
                      required
                    >
                      <SelectTrigger className="h-8">
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        {integrationStatuses.map((status) => (
                          <SelectItem key={status} value={status}>
                            {status}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="col-span-1 space-y-1">
                    <Label htmlFor="weeks" className="text-sm">Weeks</Label>
                    <Input
                      id="weeks"
                      type="number"
                      min="1"
                      value={formData.weeks || ''}
                      onChange={(e) => setFormData({ ...formData, weeks: parseInt(e.target.value) })}
                      required
                      className="h-8"
                    />
                  </div>
                  <div className="col-span-3 space-y-1">
                    <Label htmlFor="startDate" className="text-sm">Start Date</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full h-8 justify-start text-left font-normal",
                            !formData.startDate && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-2 h-3 w-3" />
                          {formData.startDate ? format(new Date(formData.startDate), "PPP") : <span>Pick a date</span>}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={formData.startDate ? new Date(formData.startDate) : undefined}
                          onSelect={(date) => setFormData({ ...formData, startDate: date })}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                  <div className="col-span-3 space-y-1">
                    <Label htmlFor="lastCommDate" className="text-sm">Last Communication</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full h-8 justify-start text-left font-normal",
                            !formData.lastCommDate && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-2 h-3 w-3" />
                          {formData.lastCommDate ? format(new Date(formData.lastCommDate), "PPP") : <span>Pick a date</span>}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={formData.lastCommDate ? new Date(formData.lastCommDate) : undefined}
                          onSelect={(date) => setFormData({ ...formData, lastCommDate: date })}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                  <div className="col-span-3 space-y-1">
                    <Label htmlFor="actualEndDate" className="text-sm flex items-center gap-1">
                      Actual End Date
                      {formData.status === 'Live' && <span className="text-purple-500">*</span>}
                    </Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full h-8 justify-start text-left font-normal",
                            !formData.actualEndDate && "text-muted-foreground",
                            errors.actualEndDate && "border-purple-500"
                          )}
                        >
                          <CalendarIcon className="mr-2 h-3 w-3" />
                          {formData.actualEndDate ? format(new Date(formData.actualEndDate), "PPP") : <span>Pick a date</span>}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={formData.actualEndDate ? new Date(formData.actualEndDate) : undefined}
                          onSelect={(date) => setFormData({ ...formData, actualEndDate: date })}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    {errors.actualEndDate && <p className="text-purple-500 text-xs">{errors.actualEndDate}</p>} {/* Inline error */}
                  </div>
                </div>
              </div>

              {/* Additional Information */}
              <div className="mb-4">
                <h3 className="text-base font-semibold mb-2 border-b pb-1">Additional Information</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-1">
                    <Label htmlFor="comments" className="text-sm">Comments</Label>
                    <Input
                      id="comments"
                      placeholder="Add comments about this integration"
                      value={formData.comments || ''}
                      onChange={(e) => setFormData({ ...formData, comments: e.target.value })}
                      className="h-8"
                    />
                  </div>
                  <div className="space-y-1">
                    <Label htmlFor="contacts" className="text-sm">Contacts</Label>
                    <Input
                      id="contacts"
                      value={formData.contact || ''}
                      readOnly
                      disabled
                      className="h-8 bg-muted"
                    />
                  </div>
                </div>
              </div>

              <DrawerFooter className="px-0 pt-2">
                <div className="flex w-full items-center justify-between">
                  <div className="flex gap-2">
                    <Button
                      type="button"
                      variant="destructive"
                      onClick={() => setShowDeleteAlert(true)}
                      disabled={isDeleting}
                      className="flex items-center gap-2 h-9"
                    >
                      <Trash2 className="h-4 w-4" />
                      Delete
                    </Button>
                    <Button
                      type="button"
                      variant="secondary"
                      onClick={() => setShowArchiveAlert(true)}
                      disabled={isArchiving}
                      className="flex items-center gap-2 h-9"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
                        <rect width="20" height="5" x="2" y="3" rx="1" />
                        <path d="M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8" />
                        <path d="M10 12h4" />
                      </svg>
                      Archive
                    </Button>
                  </div>
                  <div className="flex gap-2">
                    <Button type="submit">Update Integration</Button>
                    <DrawerClose asChild>
                      <Button variant="outline" type="button">Cancel</Button>
                    </DrawerClose>
                  </div>
                </div>
              </DrawerFooter>
            </form>
          </div>
        </DrawerContent>
      </Drawer>

      <AlertDialog open={showDeleteAlert} onOpenChange={setShowDeleteAlert}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the integration
              <span className="font-semibold"> {integration.ota}</span> with batch ID
              <span className="font-semibold"> {integration.batch}</span>.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? 'Deleting...' : 'Delete Integration'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <AlertDialog open={showArchiveAlert} onOpenChange={setShowArchiveAlert}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Archive this integration?</AlertDialogTitle>
            <AlertDialogDescription>
              This will archive the integration
              <span className="font-semibold"> {integration.ota}</span> with batch ID
              <span className="font-semibold"> {integration.batch}</span>. 
              Archived integrations can be viewed in the archive section.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleArchive}
              disabled={isArchiving}
              className="bg-secondary text-secondary-foreground hover:bg-secondary/90"
            >
              {isArchiving ? 'Archiving...' : 'Archive Integration'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}