import { NextResponse } from 'next/server';
import { connectDB, QuickWin } from '@/lib/mongodb';
import { QuickWinStats, QuickWinStatus } from '@/types';

export const runtime = 'nodejs'; // Force Node.js runtime

export async function GET() {
  try {
    await connectDB();
    
    const quickwins = await QuickWin.find({ archived: { $ne: true } });

    const stats: QuickWinStats = {
      total: quickwins.length,
      byStatus: {
        Initiation: 0,
        Onboarding: 0,
        Live: 0,
        Hold: 0
      },
      averageCompletionTime: 0,
      delayedIntegrations: 0
    };

    let completedCount = 0;
    let totalCompletionTime = 0;

    quickwins.forEach(quickwin => {
      // Count by status
      const status = quickwin.status as QuickWinStatus;
      stats.byStatus[status] = (stats.byStatus[status] || 0) + 1;

      // Check for delayed integrations
      if (quickwin.weeksElapsed > quickwin.weeks) {
        stats.delayedIntegrations++;
      }

      // Calculate average completion time for completed integrations
      if (quickwin.actualEndDate) {
        completedCount++;
        const startDate = new Date(quickwin.startDate);
        const endDate = new Date(quickwin.actualEndDate);
        totalCompletionTime += (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24 * 7); // Convert to weeks
      }
    });

    stats.averageCompletionTime = completedCount > 0 ? totalCompletionTime / completedCount : 0;

    return NextResponse.json(stats);
  } catch (error) {
    console.error('Error calculating stats:', error);
    return NextResponse.json({ error: 'Failed to calculate integration stats' }, { status: 500 });
  }
}