import { NextResponse } from 'next/server';
import { connectDB, QuickWin, MasterOTA } from '@/lib/mongodb';

export const runtime = 'nodejs';

/**
 * POST endpoint specifically for archiving quick wins
 * Only updates the archived flag and archivedDate
 */
export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB();
    
    console.log('Archiving quick win with ID:', params.id);
    
    // First, get the quick win to get its masterOtaId, type, and actualEndDate
    const quickwin = await QuickWin.findById(params.id);
    if (!quickwin) {
      return NextResponse.json({ error: 'Quick win not found' }, { status: 404 });
    }

    // Get current date for archive timestamp
    const archivedDate = new Date();
    
    // Update only the archive-related fields
    const result = await QuickWin.updateOne(
      { _id: params.id },
      { 
        $set: { 
          archived: true,
          archivedDate: archivedDate
        } 
      }
    );
    
    console.log('Archive result:', result);
    
    if (!result.acknowledged || result.matchedCount === 0) {
      return NextResponse.json({ error: 'Quick win not found' }, { status: 404 });
    }
    
    if (result.modifiedCount === 0) {
      return NextResponse.json({ 
        warning: 'Quick win found but not modified. Perhaps it is already archived?',
        success: false
      }, { status: 200 });
    }

    // If the quick win has a masterOtaId, update its usage data
    if (quickwin.masterOtaId) {
      // Map quick win type to usage field
      const integrationType = quickwin.type?.toLowerCase() || 'booking';
      
      // Create update object for master OTA
      // Set the entire integration type object structure
      const usageUpdate = {
        [`${integrationType}`]: {
          live: true,
          liveDate: quickwin.actualEndDate || archivedDate,
          lastUsedDate: null
        }
      };

      // Update the master OTA
      await MasterOTA.updateOne(
        { _id: quickwin.masterOtaId },
        { $set: usageUpdate }
      );
    }
    
    // Fetch the updated document
    const updatedDoc = await QuickWin.findById(params.id);
    
    return NextResponse.json({ 
      success: true, 
      message: 'Quick win archived successfully',
      data: updatedDoc
    });
  } catch (error) {
    console.error('Error archiving quick win:', error);
    return NextResponse.json({ error: 'Failed to archive quick win' }, { status: 500 });
  }
} 