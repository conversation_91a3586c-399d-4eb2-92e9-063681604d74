'use client';

import { useState, useMemo } from 'react';
import { MasterOTA } from '@/types';
import { Button } from '@/components/ui/button';
import { Download, Pencil, Search, Filter, Trash2, Copy, Check } from 'lucide-react';
import { EditMasterOtaDialog } from '@/components/master-otas/edit-master-ota-dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from 'sonner';
import { Input } from '../ui/input';

interface MasterOtasListProps {
  masterOtas: MasterOTA[];
  isLoading: boolean;
  onDataChange: () => void;
}

const ALL_VALUE = "all";

interface Filters {
  type?: string;
  country?: string;
  iata?: string;
}

export function MasterOtasList({ masterOtas, isLoading, onDataChange }: MasterOtasListProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [editOta, setEditOta] = useState<MasterOTA | null>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteId, setDeleteId] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [filters, setFilters] = useState<Filters>({});
  const [copiedContactId, setCopiedContactId] = useState<string | null>(null);

  const handleFilterChange = (key: keyof Filters, value: string | null) => {
    setFilters(prev => {
      const newFilters = {
        ...prev,
        [key]: value === ALL_VALUE ? undefined : value
      };
      return newFilters;
    });
  };

  const uniqueTypes = useMemo<string[]>(() => 
    Array.from(new Set((masterOtas?.map((ota) => ota.type) || [])))
      .filter(Boolean)
      .sort(),
    [masterOtas]
  );

  const uniqueCountries = useMemo<string[]>(() => 
    Array.from(new Set((masterOtas?.map((ota) => ota.country) || [])))
      .filter(Boolean)
      .sort(),
    [masterOtas]
  );

  const uniqueIatas = useMemo<string[]>(() => 
    Array.from(new Set((masterOtas?.map((ota) => ota.iata) || [])))
      .filter(Boolean)
      .sort(),
    [masterOtas]
  );

  const filteredOtas = useMemo(() => {
    return masterOtas.filter(ota => {
      // First filter by search term
      const matchesSearch = 
        ota.otaName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        ota.country.toLowerCase().includes(searchTerm.toLowerCase()) ||
        ota.iata.toLowerCase().includes(searchTerm.toLowerCase()) ||
        ota.trueIata.toLowerCase().includes(searchTerm.toLowerCase());
      
      if (!matchesSearch) return false;
      
      // Then apply specific filters
      if (filters.type && ota.type !== filters.type) return false;
      if (filters.country && ota.country !== filters.country) return false;
      if (filters.iata && ota.iata !== filters.iata) return false;
      
      return true;
    });
  }, [masterOtas, searchTerm, filters]);

  const handleEditClick = (ota: MasterOTA) => {
    setEditOta(ota);
    setEditDialogOpen(true);
  };

  const handleDeleteClick = (id: string) => {
    setDeleteId(id);
    setDeleteDialogOpen(true);
  };

  const handleDelete = async () => {
    if (!deleteId) return;
    
    try {
      const response = await fetch(`/api/master-otas/${deleteId}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete Master OTA');
      }
      
      toast.success('Master OTA deleted successfully');
      onDataChange();
    } catch (error) {
      console.error('Error deleting Master OTA:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to delete Master OTA');
    } finally {
      setDeleteDialogOpen(false);
      setDeleteId(null);
    }
  };

  const handleExport = async () => {
    try {
      // Create a CSV string
      const headers = ['OTA Name', 'Type', 'Country', 'IATA', 'True IATA', 'FOP', 'Client ID', 'User ID', 'TechPartner', 'Contact'];
      const rows = filteredOtas.map(ota => [
        ota.otaName,
        ota.type || '',
        ota.country || '',
        ota.iata || '',
        ota.trueIata || '',
        ota.fop || '',
        ota.clientId || '',
        ota.userId || '',
        ota.techPartner || '',
        ota.contact || ''
      ]);
      
      const csvContent = [headers, ...rows].map(row => row.join(',')).join('\n');
      
      // Create and download the CSV file
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'master-otas.csv';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      toast.success('Export successful');
    } catch (error) {
      console.error('Error exporting data:', error);
      toast.error('Failed to export data');
    }
  };

  const handleCopyContact = (contact: string, id: string) => {
    navigator.clipboard.writeText(contact);
    setCopiedContactId(id);
    toast.success('Contact copied to clipboard');
    
    // Reset the copied state after 2 seconds
    setTimeout(() => {
      setCopiedContactId(null);
    }, 2000);
  };

  if (isLoading) {
    return <div className="text-center py-4">Loading...</div>;
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search OTAs..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Button
          onClick={handleExport}
          className="flex items-center gap-2"
          variant="outline"
        >
          <Download className="h-4 w-4" />
          <span>Export</span>
        </Button>
      </div>

      {filteredOtas.length === 0 ? (
        <div className="text-center p-6 border rounded-md">
          {searchTerm || filters.type || filters.country || filters.iata
            ? 'No Master OTAs match your search criteria'
            : 'No Master OTAs available. Create one to get started!'}
        </div>
      ) : (
        <div className="rounded-md border">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b bg-muted/50">
                  <th className="h-10 px-3 text-left align-middle font-semibold text-xs text-gray-500">OTA Name</th>
                  <th className="h-10 px-3 text-left align-middle font-semibold text-xs text-gray-500">
                    <div className="flex items-center gap-1">
                      Type
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-5 w-5">
                            <Filter className="h-3 w-3" />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-48">
                          <Select
                            value={filters.type || ALL_VALUE}
                            onValueChange={(value) => handleFilterChange('type', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select type" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value={ALL_VALUE}>All Types</SelectItem>
                              {uniqueTypes.map((type) => (
                                <SelectItem key={type} value={type}>
                                  {type}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </PopoverContent>
                      </Popover>
                    </div>
                  </th>
                  <th className="h-10 px-3 text-left align-middle font-semibold text-xs text-gray-500">
                    <div className="flex items-center gap-1">
                      Country
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-5 w-5">
                            <Filter className="h-3 w-3" />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-48">
                          <Select
                            value={filters.country || ALL_VALUE}
                            onValueChange={(value) => handleFilterChange('country', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select country" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value={ALL_VALUE}>All Countries</SelectItem>
                              {uniqueCountries.map((country) => (
                                <SelectItem key={country} value={country}>
                                  {country}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </PopoverContent>
                      </Popover>
                    </div>
                  </th>
                  <th className="h-10 px-3 text-left align-middle font-semibold text-xs text-gray-500">
                    <div className="flex items-center gap-1">
                      IATA
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-5 w-5">
                            <Filter className="h-3 w-3" />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-48">
                          <Select
                            value={filters.iata || ALL_VALUE}
                            onValueChange={(value) => handleFilterChange('iata', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select IATA" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value={ALL_VALUE}>All IATAs</SelectItem>
                              {uniqueIatas.map((iata) => (
                                <SelectItem key={iata} value={iata}>
                                  {iata}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </PopoverContent>
                      </Popover>
                    </div>
                  </th>
                  <th className="h-10 px-3 text-left align-middle font-semibold text-xs text-gray-500">True IATA</th>
                  <th className="h-10 px-3 text-left align-middle font-semibold text-xs text-gray-500">FOP</th>
                  <th className="h-10 px-3 text-left align-middle font-semibold text-xs text-gray-500">Client ID</th>
                  <th className="h-10 px-3 text-left align-middle font-semibold text-xs text-gray-500">User ID</th>
                  <th className="h-10 px-3 text-left align-middle font-semibold text-xs text-gray-500">TechPartner</th>
                  <th className="h-10 px-3 text-left align-middle font-semibold text-xs text-gray-500 w-28">Contact</th>
                  <th className="h-10 px-3 text-center align-middle font-semibold text-xs text-gray-500">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredOtas.map((ota) => (
                  <tr key={ota._id} className="border-b">
                    <td className="px-3 py-2 text-xs font-medium">{ota.otaName}</td>
                    <td className="px-3 py-2 text-xs">{ota.type || '-'}</td>
                    <td className="px-3 py-2 text-xs">{ota.country}</td>
                    <td className="px-3 py-2 text-xs">{ota.iata}</td>
                    <td className="px-3 py-2 text-xs">{ota.trueIata}</td>
                    <td className="px-3 py-2 text-xs">{ota.fop || '-'}</td>
                    <td className="px-3 py-2 text-xs">{ota.clientId}</td>
                    <td className="px-3 py-2 text-xs">{ota.userId}</td>
                    <td className="px-3 py-2 text-xs">{ota.techPartner || '-'}</td>
                    <td className="px-3 py-2 text-xs">
                      <div className="flex items-center space-x-1 w-28">
                        <span className="truncate">{ota.contact}</span>
                        {ota.contact && (
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleCopyContact(ota.contact, ota._id)}
                            title="Copy to clipboard"
                            className="h-5 w-5 ml-1 flex-shrink-0"
                          >
                            {copiedContactId === ota._id ? (
                              <Check className="h-2.5 w-2.5 text-green-500" />
                            ) : (
                              <Copy className="h-2.5 w-2.5" />
                            )}
                          </Button>
                        )}
                      </div>
                    </td>
                    <td className="px-3 py-2 text-xs">
                      <div className="flex justify-center gap-1">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleEditClick(ota)}
                          title="Edit"
                          className="h-6 w-6"
                        >
                          <Pencil className="h-3.5 w-3.5" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleDeleteClick(ota._id)}
                          title="Delete"
                          className="h-6 w-6 text-destructive hover:text-destructive"
                        >
                          <Trash2 className="h-3.5 w-3.5" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      <div className="flex justify-between items-center mt-3">
        <div className="text-xs text-muted-foreground">
          {filteredOtas.length} OTA{filteredOtas.length !== 1 ? 's' : ''}
        </div>
      </div>

      {editOta && (
        <EditMasterOtaDialog
          masterOta={editOta}
          open={editDialogOpen}
          onOpenChange={setEditDialogOpen}
          onSuccess={onDataChange}
        />
      )}

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the Master OTA and may affect related entities.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-destructive text-destructive-foreground">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}