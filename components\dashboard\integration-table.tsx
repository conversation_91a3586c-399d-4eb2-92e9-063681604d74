"use client";

import { Integration, IntegrationType, IntegrationStatus } from "@/types";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { TablePagination } from "@/components/ui/table-pagination";
import { 
  getIntegrationStatusColor,
  getIntegrationStatusBgColor,
  formatPercentage
} from "@/lib/format-utils";
import {
  formatDate as formatDateUtil,
  calculateWeeksElapsed,
  isIntegrationDelayed
} from "@/lib/date-utils";
import { useCallback, useState, useEffect, useMemo } from "react";
import useSWR from 'swr';
import { EditIntegrationDialog } from "./edit-integration-dialog";
import { Input } from "@/components/ui/input";
import { Search, Filter, Download } from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface IntegrationTableProps {
  integrations?: Integration[];
  currentPage: number;
  onPageChange: (page: number) => void;
  onFilteredDataChange?: (data: Integration[]) => void;
}

interface Filters {
  batch?: string;
  type?: IntegrationType;
  status?: IntegrationStatus;
  country?: string;
  manager?: string;
}

const fetcher = (url: string) => fetch(url).then(res => res.json());

const integrationTypes: IntegrationType[] = ['B2B', 'B2C', 'PP', 'Meta'];
const integrationStatuses: IntegrationStatus[] = [
  'Initiation',
  'Integration',
  'Testing',
  'Demo',
  'Onboarding',
  'Live',
  'Terminated',
  'Hold'
];

const ALL_VALUE = "all";

export function IntegrationTable({ 
  currentPage, 
  onPageChange,
  onFilteredDataChange
}: IntegrationTableProps) {
  const [selectedIntegration, setSelectedIntegration] = useState<Integration | null>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeSearch, setActiveSearch] = useState('');
  const [filters, setFilters] = useState<Filters>({});
  const pageSize = 25;
  
  const { data, error, isLoading } = useSWR<{
    data: Integration[];
    total: number;
    totalPages: number;
  }>(
    `/api/integrations?page=${currentPage}&pageSize=${pageSize}&search=${encodeURIComponent(activeSearch)}`,
    fetcher,
    {
      keepPreviousData: true
    }
  );
  const totalPages = data?.totalPages || 0;


  const handleSearch = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      setActiveSearch(searchTerm);
      onPageChange(1);
    }
  };

  const handleRowClick = (integration: Integration) => {
    setSelectedIntegration(integration);
    setEditDialogOpen(true);
  };  // Use the utility function for status background colors
  const getStatusBgColor = getIntegrationStatusBgColor;
  // Use the utility function for checking delayed integrations
  const isDelayed = isIntegrationDelayed;
  // Use the utility function for date formatting
  const formatDate = formatDateUtil;

  const handleFilterChange = (key: keyof Filters, value: string | null) => {
    setFilters(prev => {
      const newFilters = {
        ...prev,
        [key]: value === ALL_VALUE ? undefined : value
      };
      return newFilters;
    });
    onPageChange(1);
  };

  const filteredData = useMemo(() => {
    if (!data?.data) return [];
    return data.data.filter((integration: Integration) => {
      if (filters.batch && integration.batch !== filters.batch) return false;
      if (filters.type && integration.type !== filters.type) return false;
      if (filters.status && integration.status !== filters.status) return false;
      if (filters.country && integration.country !== filters.country) return false;
      if (filters.manager && integration.manager !== filters.manager) return false;
      return true;
    });
  }, [data?.data, filters]);

  useEffect(() => {
    if (onFilteredDataChange) {
      onFilteredDataChange(filteredData);
    }
  }, [filteredData, onFilteredDataChange]);

  const handlePageChange = useCallback((newPage: number) => {
    onPageChange(newPage); // Update currentPage in the parent
  }, [onPageChange]);

  const uniqueBatches = useMemo<string[]>(() => 
    Array.from(new Set((data?.data?.map((i: Integration) => i.batch) || []) as string[])).sort(),
    [data?.data]
  );

  const uniqueCountries = useMemo<string[]>(() => 
      Array.from(new Set((data?.data?.map((i: Integration) => i.country) || []) as string[])).sort(),
      [data?.data]
    );

  const uniqueManagers = useMemo<string[]>(() => 
      Array.from(new Set((data?.data?.map((i: Integration) => i.manager) || []) as string[])).sort(),
      [data?.data]
    );

  //const totalFilteredPages = Math.ceil(filteredData.length / 10);
  //const totalFilteredPages = Math.ceil((data?.total || 0) / 20); // Use the total from the API
  const totalFilteredPages = totalPages;
  console.log('totalFilteredPages',totalFilteredPages); // Pagination is now handled by the TablePagination component

  const handleDialogOpenChange = (open: boolean) => {
    setEditDialogOpen(open);
    if (!open) {
      // Clear the selected integration when dialog closes
      setSelectedIntegration(null);
    }
  };

  if (isLoading) {
    return <div className="text-center py-4">Loading...</div>;
  }

  if (error) {
    return <div className="text-center py-4 text-red-500">Error loading data</div>;
  }

  if (!data?.data || !Array.isArray(data.data)) {
    return (
      <div className="p-4 text-center text-gray-500">
        No integration data available
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search integrations... (Press Enter to search)"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onKeyDown={handleSearch}
            className="pl-10"
          />
        </div>
        <Button
          onClick={() => window.open('/api/integrations/export', '_blank')}
          className="flex items-center gap-2"
          variant="outline"
        >
          <Download className="h-4 w-4" />
          <span>Export</span>
        </Button>
      </div>
      
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="font-semibold">
                <div className="flex items-center gap-2">
                  Batch
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-6 w-6">
                        <Filter className="h-4 w-4" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-48">
                      <Select
                        value={filters.batch || ALL_VALUE}
                        onValueChange={(value) => handleFilterChange('batch', value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select batch" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value={ALL_VALUE}>All Batches</SelectItem>
                          {uniqueBatches.map((batch) => (
                            <SelectItem key={batch} value={batch}>
                              {batch}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </PopoverContent>
                  </Popover>
                </div>
              </TableHead>
              <TableHead className="font-semibold">OTA</TableHead>
              <TableHead className="font-semibold">
                <div className="flex items-center gap-2">
                  Type
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-6 w-6">
                        <Filter className="h-4 w-4" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-48">
                      <Select
                        value={filters.type || ALL_VALUE}
                        onValueChange={(value) => handleFilterChange('type', value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value={ALL_VALUE}>All Types</SelectItem>
                          {integrationTypes.map((type) => (
                            <SelectItem key={type} value={type}>
                              {type}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </PopoverContent>
                  </Popover>
                </div>
              </TableHead>
              <TableHead className="font-semibold">
                <div className="flex items-center gap-2">
                  Country
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-6 w-6">
                        <Filter className="h-4 w-4" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-48">
                      <Select
                        value={filters.country || ALL_VALUE}
                        onValueChange={(value) => handleFilterChange('country', value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select country" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value={ALL_VALUE}>All Countries</SelectItem>
                          {uniqueCountries.map((country) => (
                            <SelectItem key={country} value={country}>
                              {country}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </PopoverContent>
                  </Popover>
                </div>
              </TableHead>
              <TableHead className="font-semibold">FOP</TableHead>
              <TableHead className="font-semibold">IATA</TableHead>
              <TableHead className="font-semibold">True IATA</TableHead>
              <TableHead className="font-semibold">Start Date</TableHead>
              <TableHead className="font-semibold">Progress</TableHead>
              <TableHead className="font-semibold">
                <div className="flex items-center gap-2">
                  Status
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-6 w-6">
                        <Filter className="h-4 w-4" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-48">
                      <Select
                        value={filters.status || ALL_VALUE}
                        onValueChange={(value) => handleFilterChange('status', value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value={ALL_VALUE}>All Statuses</SelectItem>
                          {integrationStatuses.map((status) => (
                            <SelectItem key={status} value={status}>
                              {status}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </PopoverContent>
                  </Popover>
                </div>
              </TableHead>
              <TableHead className="font-semibold">Last Comm.</TableHead>
              <TableHead className="font-semibold">Expected End</TableHead>
              <TableHead className="font-semibold">
                <div className="flex items-center gap-2">
                  Manager
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-6 w-6">
                        <Filter className="h-4 w-4" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-48">
                      <Select
                        value={filters.manager || ALL_VALUE}
                        onValueChange={(value) => handleFilterChange('manager', value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select manager" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value={ALL_VALUE}>All Managers</SelectItem>
                          {uniqueManagers.map((manager) => (
                            <SelectItem key={manager} value={manager}>
                              {manager}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </PopoverContent>
                  </Popover>
                </div>
              </TableHead>
              <TableHead className="font-semibold">Actual End</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredData.map((integration: Integration, rowIndex: number) => {
              const weeksElapsed: number = calculateWeeksElapsed(
                integration.startDate,
                integration.actualEndDate || undefined
              );

              return (
                <TableRow
                key={integration._id?.toString() || rowIndex}
                className={`${
                  isDelayed({ ...integration, weeksElapsed }) ? "text-red-800" : ""
                } cursor-pointer hover:bg-muted`}
                onClick={() => handleRowClick(integration)}
              >
                <TableCell className="font-medium">{integration.batch}</TableCell>
                <TableCell>{integration.ota}</TableCell>
                <TableCell>{integration.type}</TableCell>
                <TableCell>{integration.country}</TableCell>
                <TableCell>{integration.fop}</TableCell>
                <TableCell>{integration.iata}</TableCell>
                <TableCell>{integration.trueIata}</TableCell>
                <TableCell>{formatDate(integration.startDate)}</TableCell>
                <TableCell>
                {weeksElapsed.toFixed(1)} of {integration.weeks} weeks
                </TableCell>
                <TableCell>                <Badge
                  className={`${getStatusBgColor(integration.status)} text-white`}
                >
                  {integration.status}
                </Badge>
                </TableCell>
                <TableCell>{formatDate(integration.lastCommDate)}</TableCell>
                <TableCell>{formatDate(integration.expectedEndDate)}</TableCell>
                <TableCell>{integration.manager}</TableCell>
                <TableCell>{formatDate(integration.actualEndDate)}</TableCell>
              </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>        
      {filteredData.length > 0 && (
        <TablePagination
          currentPage={currentPage}
          totalPages={totalFilteredPages}
          onPageChange={handlePageChange}
          maxVisiblePages={3}
          totalItems={filteredData.length}
          pageSize={pageSize}
        />
      )}
      <EditIntegrationDialog
        integration={selectedIntegration}
        open={editDialogOpen}
        onOpenChange={handleDialogOpenChange}
      />
    </div>
  );
}