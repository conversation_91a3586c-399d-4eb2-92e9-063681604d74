'use client';

import { useState, useMemo } from 'react';
import useSWR from 'swr';
import { QuickWin, QuickWinStatus, QuickWinStats } from '@/types';
import { fetcher } from '@/lib/api-utils';

/**
 * Custom hook for managing QuickWin stats
 * @returns Stats data and related state/functions
 */
export function useQuickWinStats() {
  const [filteredData, setFilteredData] = useState<QuickWin[]>([]);
  
  // Fetch stats from API
  const { data: stats, error: statsError } = useSWR<QuickWinStats>('/api/quickwins/stats', fetcher);

  /**
   * Calculate stats based on filtered data
   */
  const calculateFilteredStats = (data: QuickWin[]): QuickWinStats => {
    if (!data || data.length === 0) {
      return {
        total: 0,
        byStatus: {
          Initiation: 0,
          Onboarding: 0,
          Live: 0,
          Hold: 0,
        },
        averageCompletionTime: 0,
        delayedIntegrations: 0,
      };
    }

    const byStatus: Record<QuickWinStatus, number> = {
      Initiation: 0,
      Onboarding: 0,
      Live: 0,
      Hold: 0,
    };

    let completedCount = 0;
    let totalCompletionTime = 0;
    let delayedCount = 0;

    data.forEach(quickwin => {
      // Count by status
      if (byStatus.hasOwnProperty(quickwin.status)) {
        byStatus[quickwin.status as QuickWinStatus]++;
      }

      // Check for delayed integrations
      if (quickwin.weeksElapsed > quickwin.weeks) {
        delayedCount++;
      }

      // Calculate average completion time for completed integrations
      if (quickwin.actualEndDate) {
        completedCount++;
        const startDate = new Date(quickwin.startDate);
        const endDate = new Date(quickwin.actualEndDate);
        totalCompletionTime += (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24 * 7); // Convert to weeks
      }
    });

    return {
      total: data.length,
      byStatus,
      averageCompletionTime: completedCount > 0 ? totalCompletionTime / completedCount : 0,
      delayedIntegrations: delayedCount,
    };
  };

  // Calculate display stats based on filtered data or original stats
  const displayStats = useMemo(() => {
    return filteredData.length > 0
      ? calculateFilteredStats(filteredData)
      : stats || calculateFilteredStats([]);
  }, [filteredData, stats]);

  return {
    stats,
    statsError,
    filteredData,
    setFilteredData,
    displayStats,
    isLoading: !stats && !statsError,
  };
}
