import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>cle,
  <PERSON><PERSON>ircle,
  <PERSON><PERSON><PERSON><PERSON>gle,
  AlertOctagon
} from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";
import { formatLargeNumber, getStatusColor, getStatusBgColor, getStatusLightBgColor } from '@/lib/format-utils';

interface StatCardsProps {
  totalCounts: {
    Success: number;
    WrongRoutes: number;
    Throttled: number;
    Failures: number;
    Bookings?: number;
    AverageLookToBook?: number;
  };
  percentages: {
    Success: number;
    WrongRoutes: number;
    Throttled: number;
    Failures: number;
  };
  percentageChanges?: {
    Success: number;
    WrongRoutes: number;
    Throttled: number;
    Failures: number;
  };
  analyticsData?: {
    Date: string;
    Success: string;
    WrongRoutes: string;
    Throttled: string;
    Failures: string;
  }[];
  searchTerm?: string;
  variant?: 'horizontal' | 'vertical';
  compact?: boolean;
  className?: string;
  showHeader?: boolean;
}

export default function StatCards({
  totalCounts,
  percentages,
  percentageChanges,
  searchTerm = "",
  variant = 'horizontal',
  compact = false,
  className = "",
  showHeader = true
}: StatCardsProps) {
  type StatKey = keyof typeof percentages;

  const stats = [
    {      
      id: 1,
      title: "Success",
      dataKey: "Success" as StatKey,
      value: formatLargeNumber(totalCounts.Success),
      percentage: `${percentages.Success.toFixed(1)}%`,
      icon: CheckCircle,
      color: getStatusColor("success"),
      bgColor: getStatusBgColor("success"),
      lightBgColor: getStatusLightBgColor("success"),
      change: percentageChanges?.Success || 0,
      changeType: (percentageChanges?.Success || 0) >= 0 ? "increase" : "decrease"
    },
    {      
      id: 2,
      title: "Non-Operating Routes",
      dataKey: "WrongRoutes" as StatKey,
      value: formatLargeNumber(totalCounts.WrongRoutes),
      percentage: `${percentages.WrongRoutes.toFixed(1)}%`,
      icon: AlertTriangle,
      color: getStatusColor("wrongRoutes"),
      bgColor: getStatusBgColor("wrongRoutes"),
      lightBgColor: getStatusLightBgColor("wrongRoutes"),
      change: percentageChanges?.WrongRoutes || 0,
      changeType: (percentageChanges?.WrongRoutes || 0) >= 0 ? "increase" : "decrease"
    },
    {      
      id: 3,
      title: "Throttled",
      dataKey: "Throttled" as StatKey,
      value: formatLargeNumber(totalCounts.Throttled),
      percentage: `${percentages.Throttled.toFixed(1)}%`,
      icon: AlertOctagon,
      color: getStatusColor("throttled"),
      bgColor: getStatusBgColor("throttled"),
      lightBgColor: getStatusLightBgColor("throttled"),
      change: percentageChanges?.Throttled || 0,
      changeType: (percentageChanges?.Throttled || 0) >= 0 ? "increase" : "decrease"
    },
    {      
      id: 4,
      title: "Failures",
      dataKey: "Failures" as StatKey,
      value: formatLargeNumber(totalCounts.Failures),
      percentage: `${percentages.Failures.toFixed(1)}%`,
      icon: XCircle,
      color: getStatusColor("failures"),
      bgColor: getStatusBgColor("failures"),
      lightBgColor: getStatusLightBgColor("failures"),
      change: percentageChanges?.Failures || 0,
      changeType: (percentageChanges?.Failures || 0) >= 0 ? "increase" : "decrease"
    }
  ];  // Render different layouts based on the variant
  if (variant === 'vertical') {
    return (
      <div className={cn("space-y-3", className)}>
        {showHeader && (
          <div className="text-sm font-medium text-muted-foreground mb-1">
            Shopping Transactions
            {searchTerm && <span className="text-xs ml-1">for "{searchTerm}"</span>}
          </div>
        )}

        {stats.map((stat) => (
          <Card key={stat.id} className={cn("overflow-hidden shadow-sm", compact ? "p-0" : "")}>
            <div className={cn("flex justify-between items-center", compact ? "p-2.5" : "p-3", stat.lightBgColor)}>
              <div className="flex gap-2 items-center">
                <stat.icon className={cn("h-4 w-4", stat.color)} />
                <div className="text-sm font-medium">{stat.title}</div>
              </div>
              <div className={cn("font-medium", stat.color, "text-sm")}>
                {stat.percentage}
              </div>
            </div>

            <CardContent className={cn("flex justify-between items-center", compact ? "p-2.5 pt-2" : "py-2 px-3")}>
              <div className={cn("font-bold", compact ? "text-base" : "text-lg")}>
                {stat.value}
              </div>

              {percentageChanges && (
                <div className="text-xs flex items-center">
                  {stat.changeType === 'increase' ? (
                    <div className="flex items-center">
                      <ArrowUp className={cn("mr-0.5", compact ? "h-2.5 w-2.5" : "h-3 w-3", "text-green-600")} />
                      <span className="text-green-600">{Math.abs(stat.change).toFixed(1)}%</span>
                    </div>
                  ) : (
                    <div className="flex items-center">
                      <ArrowDown className={cn("mr-0.5", compact ? "h-2.5 w-2.5" : "h-3 w-3", "text-red-600")} />
                      <span className="text-red-600">{Math.abs(stat.change).toFixed(1)}%</span>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  // Default horizontal layout
  return (
    <div className={cn("mb-8", className)}>
      {showHeader && (
        <h2 className="text-xl font-semibold mb-4">
          Shopping Transactions
          {searchTerm && <span className="text-base font-normal text-muted-foreground ml-2">for "{searchTerm}"</span>}
        </h2>
      )}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {stats.map((stat) => (
          <Card key={stat.id} className="overflow-hidden">
            <CardHeader className={cn(stat.lightBgColor, "pb-2")}>
              <div className="flex justify-between items-start">
                <CardTitle className="text-lg font-medium">{stat.title}</CardTitle>
                <stat.icon className={cn("h-5 w-5", stat.color)} />
              </div>
            </CardHeader>
            <CardContent className="pt-4">
              <div className="text-2xl font-bold mb-1">
                {stat.value}
              </div>
              <div className="flex items-center text-sm text-muted-foreground mb-2">
                <span className="font-medium mr-2">{stat.percentage} of total</span>
                {percentageChanges && (
                  <span className={cn(
                    "flex items-center",
                    stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600'
                  )}>
                    {stat.changeType === 'increase' ? (
                      <ArrowUp className="h-3 w-3 mr-1" />
                    ) : (
                      <ArrowDown className="h-3 w-3 mr-1" />
                    )}
                    {Math.abs(stat.change).toFixed(1)}%
                  </span>
                )}
              </div>
              <Progress
                value={percentages[stat.dataKey]}
                className="h-1.5"
                indicatorClassName={stat.bgColor}
              />
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}