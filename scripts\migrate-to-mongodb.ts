import fs from 'fs';
import path from 'path';
import { connectDB, Looks, Books } from '../lib/mongodb';

const readCSVFile = (filePath: string): string[] => {
  if (!fs.existsSync(filePath)) {
    throw new Error(`File not found: ${filePath}`);
  }
  return fs.readFileSync(filePath, 'utf-8').split('\n').filter((line) => line.trim() !== '');
};

const migrateLooksData = async () => {
  const looksFilePath = path.join(process.cwd(), 'data', 'looks.csv');
  const looksRows = readCSVFile(looksFilePath);
  const [header, ...rows] = looksRows;
  const columns = header.split(',').map((col) => col.trim());

  const looksData = rows.map((row) => {
    const values = row.split(',').map((item) => item.trim());
    return {
      date: values[columns.indexOf('Date')],
      otaName: values[columns.indexOf('OTAName')],
      success: parseInt(values[columns.indexOf('Success')] || '0', 10),
      wrongRoutes: parseInt(values[columns.indexOf('WrongRoutes')] || '0', 10),
      throttled: parseInt(values[columns.indexOf('Throttled')] || '0', 10),
      failures: parseInt(values[columns.indexOf('Errors')] || '0', 10),
    };
  });

  // Insert looks data into MongoDB
  await Looks.insertMany(looksData);
  console.log(`Migrated ${looksData.length} looks records`);
};

const migrateBooksData = async () => {
  const booksFilePath = path.join(process.cwd(), 'data', 'books.csv');
  const booksRows = readCSVFile(booksFilePath);
  const [header, ...rows] = booksRows;

  const booksData = rows.map((row) => {
    const values = row.split(',').map((value) => value.trim());
    return {
      date: values[0],
      otaName: values[2],
      bookingCount: parseInt(values[3] || '0', 10),
    };
  });

  // Insert books data into MongoDB
  await Books.insertMany(booksData);
  console.log(`Migrated ${booksData.length} books records`);
};

const migrateData = async () => {
  try {
    await connectDB();
    console.log('Connected to MongoDB');

    // Clear existing data
    await Looks.deleteMany({});
    await Books.deleteMany({});
    console.log('Cleared existing data');

    // Migrate data
    await migrateLooksData();
    await migrateBooksData();

    console.log('Migration completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
};

migrateData(); 