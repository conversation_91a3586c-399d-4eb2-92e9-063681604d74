import { NextResponse } from 'next/server';
import * as XLSX from 'xlsx';
import { connectDB, QuickWin } from '@/lib/mongodb';
import { format } from 'date-fns';

export async function GET() {
  try {
    await connectDB();
    
    // Fetch all quickwins with populated masterOtaId
    const quickwins = await QuickWin.find({})
      .populate('masterOtaId', 'otaName type country iata trueIata fop clientId userId contact');
    
    // Transform data for Excel
    const data = quickwins.map(quickwin => {
      const masterOta = quickwin.masterOtaId || {};
      
      // Safe date formatting function
      const formatDate = (dateValue: Date | string | null | undefined): string => {
        if (!dateValue) return 'N/A';
        try {
          const date = new Date(dateValue);
          // Check if date is valid
          if (isNaN(date.getTime())) return 'N/A';
          return format(date, 'dd-MMM-yy');
        } catch (error) {
          console.error(`Error formatting date: ${dateValue}`, error);
          return 'N/A';
        }
      };

      const startDate = formatDate(quickwin.startDate);
      const lastCommDate = formatDate(quickwin.lastCommDate);
      const actualEndDate = formatDate(quickwin.actualEndDate);
      
      // Calculate weeks elapsed with validation
      let weeksElapsed = 0;
      if (quickwin.startDate) {
        try {
          const start = new Date(quickwin.startDate);
          if (!isNaN(start.getTime())) {
            weeksElapsed = Math.round(((new Date().getTime() - start.getTime()) / (1000 * 60 * 60 * 24 * 7)) * 10) / 10;
          }
        } catch (error) {
          console.error(`Error calculating weeks elapsed for quickwin ${quickwin._id}:`, error);
        }
      }

      return {
        'Batch': quickwin.batch || 'N/A',
        'OTA': masterOta.otaName || 'N/A',
        'Type': masterOta.type || 'N/A',
        'Country': masterOta.country || 'N/A',
        'Form of Payment': masterOta.fop || 'N/A',
        'IATA': masterOta.iata || 'N/A',
        'True IATA': masterOta.trueIata || 'N/A',
        'Client ID': masterOta.clientId || 'N/A',
        'User ID': masterOta.userId || 'N/A',
        'Contact': masterOta.contact || 'N/A',
        'Start Date': startDate,
        'Weeks': quickwin.weeks || 0,
        'Weeks Elapsed': weeksElapsed,
        'Status': quickwin.status || 'N/A',
        'Last Communication': lastCommDate,
        'Expected End Date': formatDate(quickwin.expectedEndDate),
        'Actual End Date': actualEndDate,
        'Manager': quickwin.manager || 'N/A',
        'Comments': quickwin.comments || ''
      };
    });

    // Create workbook and worksheet
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.json_to_sheet(data);

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(wb, ws, 'Quickwins');

    // Generate buffer
    const buf = XLSX.write(wb, { type: 'buffer', bookType: 'xlsx' });

    // Return the Excel file
    return new NextResponse(buf, {
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': 'attachment; filename=quickwins.xlsx'
      }
    });

  } catch (error) {
    console.error('Error exporting quickwins:', error);
    return NextResponse.json({ error: 'Failed to export quickwins' }, { status: 500 });
  }
}
