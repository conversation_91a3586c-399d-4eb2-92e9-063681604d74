import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  // Get the pathname
  const path = request.nextUrl.pathname;
  

  //console.log('path', path);
  // Public paths that don't require authentication
  const publicPaths = ['/login', '/auth/callback'];

  // Check if the path is public
  const isPublicPath = publicPaths.some(publicPath => path.startsWith(publicPath));

  // Get the token from the session
  const token = request.cookies.get('id_token');

  //console.log('token', token);

  // If the path is not public and there's no token, redirect to login
  if (!isPublicPath && !token) {
    const loginUrl = new URL('/login', request.url);
    return NextResponse.redirect(loginUrl);
  }

  // If we're on the login page but have a token, redirect to home
  if (path === '/login' && token) {
    const homeUrl = new URL('/', request.url);
    return NextResponse.redirect(homeUrl);
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico).*)', // Matches all paths except static assets
    '/api/:path*', // Explicitly include API routes
  ],
};