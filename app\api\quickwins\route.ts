import { NextRequest, NextResponse } from 'next/server';
import { connectDB, QuickWin } from '../../../lib/mongodb';
import mongoose from 'mongoose';

export const runtime = 'nodejs'; // Force Node.js runtime

export async function GET(req: Request) {
  try {
    await connectDB();
    
    const url = new URL(req.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const pageSize = parseInt(url.searchParams.get('pageSize') || '10');
    const search = url.searchParams.get('search') || '';
    const skip = (page - 1) * pageSize;

    // Create aggregation pipeline
    const pipeline: mongoose.PipelineStage[] = [];
    
    // Lookup stage for populating masterOtaId
    pipeline.push({
      $lookup: {
        from: 'masterotas', // Collection name may need to be adjusted based on your actual collection name
        localField: 'masterOtaId',
        foreignField: '_id',
        as: 'masterOtaData'
      }
    });
    
    // Unwind the array created by lookup
    pipeline.push({
      $unwind: {
        path: '$masterOtaData',
        preserveNullAndEmptyArrays: true
      }
    });
    
    // Match stage for search if a search term is provided
    if (search) {
      pipeline.push({
        $match: {
          $and: [
            {
              $or: [
                { archived: { $exists: false } },
                { archived: false }
              ]
            },
            {
              $or: [
                { batch: { $regex: search, $options: 'i' } },
                { status: { $regex: search, $options: 'i' } },
                { 'masterOtaData.otaName': { $regex: search, $options: 'i' } },
                { 'masterOtaData.country': { $regex: search, $options: 'i' } },
                { 'masterOtaData.clientId': { $regex: search, $options: 'i' } }
              ]
            }
          ]
        }
      });
    } else {
      // Add match stage for archived filter even when there's no search
      pipeline.push({
        $match: {
          $or: [
            { archived: { $exists: false } },
            { archived: false }
          ]
        }
      });
    }
    
    // Sort by startDate
    pipeline.push({
      $sort: { startDate: 1 }
    } as mongoose.PipelineStage);
    
    // Get total count before pagination
    const countPipeline = [...pipeline];
    countPipeline.push({ $count: 'total' } as mongoose.PipelineStage);
    
    // Add pagination
    pipeline.push({ $skip: skip } as mongoose.PipelineStage);
    pipeline.push({ $limit: pageSize } as mongoose.PipelineStage);
    
    // Execute queries
    const [results, countResult] = await Promise.all([
      QuickWin.aggregate(pipeline),
      QuickWin.aggregate(countPipeline)
    ]);
    
    const total = countResult.length > 0 ? countResult[0].total : 0;
    const totalPages = Math.ceil(total / pageSize);
    
    // Process results to match the expected format
    const currentDate = new Date();
    const quickwinsWithWeeksElapsed = results.map((quickwin) => {
      const startDate = new Date(quickwin.startDate);
      const endDate = quickwin.actualEndDate 
        ? new Date(quickwin.actualEndDate) 
        : currentDate;
      const timeDifference = endDate.getTime() - startDate.getTime();
      const weeksElapsed = Math.max(0, Math.round((timeDifference / (1000 * 60 * 60 * 24 * 7)) * 10) / 10);

      const expectedEndDate = new Date(startDate);
      expectedEndDate.setDate(expectedEndDate.getDate() + (quickwin.weeks * 7));

      // Get the masterOta data from the lookup result
      const masterOta = quickwin.masterOtaData || {};
      
      return {
        ...quickwin,
        masterOtaId: quickwin.masterOtaId, // Preserve the original ID reference
        masterOtaData: undefined, // Remove the expanded data to match original format
        weeksElapsed,
        expectedEndDate,
        ota: masterOta.otaName || 'N/A',
        type: masterOta.type || 'N/A',
        country: masterOta.country || 'N/A',
        iata: masterOta.iata || 'N/A',
        trueIata: masterOta.trueIata || 'N/A',
        fop: masterOta.fop || 'N/A',
        clientId: masterOta.clientId || 'N/A',
        userId: masterOta.userId || 'N/A',
        contact: masterOta.contact || 'N/A'
      };
    });

    return NextResponse.json({
      data: quickwinsWithWeeksElapsed,
      total,
      page,
      pageSize,
      totalPages,
    });
  } catch (error) {
    console.error('Error fetching quickwins:', error);
    return NextResponse.json(
      { error: 'Failed to load quickwins data' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectDB();
    
    const data = await request.json();
    
    // Validate required fields
    if (!data.masterOtaId) {
      return NextResponse.json({ error: 'masterOtaId is required' }, { status: 400 });
    }

    const startDate = new Date(data.startDate);
    const expectedEndDate = new Date(startDate);
    expectedEndDate.setDate(expectedEndDate.getDate() + (data.weeks * 7));

    const currentDate = new Date();
    const endDate = data.actualEndDate && !isNaN(Date.parse(data.actualEndDate))
      ? new Date(data.actualEndDate)
      : currentDate;
    const timeDifference = endDate.getTime() - startDate.getTime();
    const weeksElapsed = Math.round((timeDifference / (1000 * 60 * 60 * 24 * 7)) * 10) / 10;

    const quickwin = await QuickWin.create({
      batch: data.batch,
      ota: data.ota,
      masterOtaId: data.masterOtaId,
      startDate,
      expectedEndDate,
      weeksElapsed,
      weeks: parseInt(data.weeks),
      status: data.status || 'Initiation',
      manager: data.manager || '',
      actualEndDate: data.actualEndDate && !isNaN(Date.parse(data.actualEndDate)) ? new Date(data.actualEndDate) : null,
      comments: data.comments || '',
      archived: false,
      type: data.type,
      country: data.country,
      iata: data.iata,
      trueIata: data.trueIata,
      fop: data.fop,
      clientId: data.clientId,
      userId: data.userId,
      contact: data.contact
    });

    return NextResponse.json({
      success: true,
      data: {
        ...quickwin.toObject(),
        weeksElapsed,
        expectedEndDate,
      },
    });
  } catch (error) {
    console.error('Error creating quickwin:', error);
    return NextResponse.json({ error: 'Failed to create quickwin' }, { status: 500 });
  }
}