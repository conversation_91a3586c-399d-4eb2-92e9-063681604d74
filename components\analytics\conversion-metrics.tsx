"use client"

import * as React from "react"
import { <PERSON>, <PERSON><PERSON>hart, CartesianGrid, XAxis } from "recharts"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"
import TopOTAsCard from "./top-otas-card"
import WorstOTAsCard from "./poor-searches-otas-card"
import BottomOTAsCard from "./bottom-otas-card"
import HighestLookToBookOTAsCard from "./highest-looktobook-otas-card"
import { formatLargeNumber, getStatusColor, getChartColor } from "@/lib/format-utils"
import { ExportOptions } from "./export-options"

interface ConversionMetricsProps {
  searchTerm?: string;
}

interface DailyData {
  date: string;
  Success: number;
  WrongRoutes: number;
  Throttled: number;
  Failures: number;
  Bookings: number;
  OTAName?: string;
}

const chartConfig = {
  looks: {
    label: "Looks",
    color: getChartColor("success"),  // maps to Success in the data
  },
  bookings: {
    label: "Bookings",
    color: getChartColor("bookings"),
  },  lookToBook: {
    label: "Look2Book",
    color: getChartColor("looktobook"),
  },
  wrongRoutes: {
    label: "Routes",
    color: getChartColor("wrongroutes"),
  },
  throttled: {
    label: "Throttled",
    color: getChartColor("throttled"),
  },
  errors: {
    label: "Errors",
    color: getChartColor("failures"),  // maps to Failures in the data
  },
} satisfies ChartConfig

const ConversionMetrics = ({ searchTerm = "" }: ConversionMetricsProps) => {
  const [activeChart, setActiveChart] = React.useState<keyof typeof chartConfig>("looks")
  const [dailyData, setDailyData] = React.useState<DailyData[]>([])

  // Fetch daily data
  React.useEffect(() => {
    const fetchDailyData = async () => {
      try {
        const searchParam = searchTerm ? `?search=${encodeURIComponent(searchTerm)}` : '';

        const [looksResponse, booksResponse] = await Promise.all([
          fetch(`/api/analytics-mongo/daily-looks${searchParam}`),
          fetch(`/api/analytics-mongo/daily-books${searchParam}`)
        ]);

        const looksData = await looksResponse.json();
        const booksData = await booksResponse.json();

        // Check if we have OTA-specific data
        const hasOtaData = looksData.data.some((item: any) => item.OTAName);

        // Combine the data
        const combinedData = looksData.data.map((look: any) => {
          // Match books data either by date alone, or by date and OTA name when available
          const book = hasOtaData && look.OTAName
            ? booksData.data.find((b: any) =>
                b.date === look.date && b.OTAName === look.OTAName)
            : booksData.data.find((b: any) => b.date === look.date);

          return {
            date: look.date,
            Success: look.Success,
            WrongRoutes: look.WrongRoutes,
            Throttled: look.Throttled,
            Failures: look.Failures,
            OTAName: look.OTAName,
            Bookings: book?.BookingCounts || 0
          };
        });

        // Sort by date
        combinedData.sort((a: DailyData, b: DailyData) =>
          new Date(a.date).getTime() - new Date(b.date).getTime()
        );

        setDailyData(combinedData);
      } catch (error) {
        console.error('Error fetching daily data:', error);
      }
    };

    fetchDailyData();
  }, [searchTerm]);

  // Process the data for the chart
  const chartData = React.useMemo(() => {
    if (!dailyData || dailyData.length === 0) return [];

    // If we have OTA data, we may want to group by date for the chart
    const hasOtaData = dailyData.some(item => item.OTAName);

    if (hasOtaData && searchTerm) {
      // Use the data as is for OTA-specific searches
      return dailyData.map(item => {
        // Calculate look-to-book ratio for each day
        const lookToBook = item.Bookings > 0 ? Math.round(item.Success / item.Bookings) : 0;

        return {
          date: item.date + (item.OTAName ? ` (${item.OTAName})` : ''),
          looks: item.Success,
          bookings: item.Bookings,
          lookToBook: lookToBook,
          wrongRoutes: item.WrongRoutes,
          throttled: item.Throttled,
          errors: item.Failures
        };
      });
    } else {
      // Group by date for general views or when no search
      const groupedByDate = new Map<string, {
        looks: number;
        bookings: number;
        wrongRoutes: number;
        throttled: number;
        errors: number;
        lookToBook: number;
      }>();

      dailyData.forEach(item => {
        if (!groupedByDate.has(item.date)) {
          groupedByDate.set(item.date, {
            looks: 0,
            bookings: 0,
            wrongRoutes: 0,
            throttled: 0,
            errors: 0,
            lookToBook: 0
          });
        }
        const group = groupedByDate.get(item.date)!;
        group.looks += item.Success;
        group.bookings += item.Bookings;
        group.wrongRoutes += item.WrongRoutes;
        group.throttled += item.Throttled;
        group.errors += item.Failures;
      });

      // Calculate look-to-book ratio for each grouped date
      groupedByDate.forEach((value) => {
        value.lookToBook = value.bookings > 0 ? Math.round(value.looks / value.bookings) : 0;
      });

      return Array.from(groupedByDate.entries()).map(([date, values]) => ({
        date,
        ...values
      }));
    }
  }, [dailyData, searchTerm]);

  const total = React.useMemo(
    () => {
      const totals = {
        looks: chartData.reduce((acc, curr) => acc + curr.looks, 0),
        bookings: chartData.reduce((acc, curr) => acc + curr.bookings, 0),
        wrongRoutes: chartData.reduce((acc, curr) => acc + curr.wrongRoutes, 0),
        throttled: chartData.reduce((acc, curr) => acc + curr.throttled, 0),
        errors: chartData.reduce((acc, curr) => acc + curr.errors, 0),
        lookToBook: 0
      };

      // Calculate the overall look-to-book ratio
      totals.lookToBook = totals.bookings > 0 ? Math.round(totals.looks / totals.bookings) : 0;

      return totals;
    },
    [chartData]
  );
  return (
    <div className="mb-8">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
        <Card className="lg:col-span-2">
          <CardHeader className="flex flex-col items-stretch space-y-0 border-b p-0 sm:flex-row">
            <div className="flex flex-1 flex-col justify-center gap-1 px-6 py-5 sm:py-6">
              <CardTitle>Daily Transactions</CardTitle>
              <CardDescription>
                Showing daily transaction data
              </CardDescription>
            </div>
            <div className="flex flex-wrap">
              {["looks", "bookings", "lookToBook", "wrongRoutes", "throttled", "errors"].map((key) => {
                const chart = key as keyof typeof chartConfig

                return (
                  <button
                    key={chart}
                    data-active={activeChart === chart}
                    className="relative z-30 flex flex-1 flex-col justify-center gap-1 border-t px-4 py-3 text-left even:border-l data-[active=true]:bg-muted/50 sm:border-l sm:border-t-0 sm:px-4 sm:py-4"
                    onClick={() => setActiveChart(chart)}
                  >
                    <span className="text-xs text-muted-foreground">
                      {chartConfig[chart].label}
                    </span>                    
                    <span className={`text-lg font-bold leading-none sm:text-2xl ${getStatusColor(key.toLowerCase())}`}>
                      {formatLargeNumber(total[key as keyof typeof total])}
                    </span>
                  </button>
                )
              })}
            </div>
          </CardHeader>
          <CardContent className="px-2 sm:p-6">
            <ChartContainer
              config={chartConfig}
              className="aspect-auto h-[380px] w-full"
            >
              <BarChart
                accessibilityLayer
                data={chartData}
                margin={{
                  left: 12,
                  right: 12,
                }}
              >
                <CartesianGrid vertical={false} />
                <XAxis
                  dataKey="date"
                  tickLine={false}
                  axisLine={false}
                  tickMargin={8}
                  minTickGap={32}
                  tickFormatter={(value) => {
                    const date = new Date(value)
                    return date.toLocaleDateString("en-US", {
                      month: "short",
                      day: "numeric",
                    })
                  }}
                />
                <ChartTooltip
                  content={
                    <ChartTooltipContent
                      className="w-[150px]"
                      nameKey="views"
                      labelFormatter={(value) => {
                        return new Date(value).toLocaleDateString("en-US", {
                          month: "short",
                          day: "numeric",
                          year: "numeric",
                        })
                      }}
                    />
                  }
                />                <Bar
                  dataKey={activeChart}
                  fill={getChartColor(activeChart.toLowerCase())}
                />
              </BarChart>
            </ChartContainer>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-col items-stretch">            
          </CardHeader>
          <CardContent>
            <ExportOptions />
          </CardContent>
        </Card>
      </div>
      {/* OTA Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-6 w-full">
        <div>
          <TopOTAsCard/>
        </div>
        <div>
          <WorstOTAsCard/>
        </div>
        <div>
          <BottomOTAsCard/>
        </div>        
        <div>
          <HighestLookToBookOTAsCard/>
        </div>
      </div>
    </div>
  );
};

export default ConversionMetrics;