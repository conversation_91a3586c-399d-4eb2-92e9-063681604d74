'use client';

import { Suspense } from 'react';
import { Header } from "@/components/header";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import LiveIntegrationsTable from '@/components/live-integrations/liveintegrations-table';
import useSWR from 'swr';

const fetcher = (url: string) => fetch(url).then(res => res.json());

// Separate component that uses useSearchParams
function LiveIntegrationsContent() {
  const { useSearchParams } = require('next/navigation');
  const searchParams = useSearchParams();
  const params = new URLSearchParams(searchParams.toString());
  
  // Use SWR for client-side data fetching with URL parameters
  const { data, error, isLoading } = useSWR(
    `/api/live-integrations?${params.toString()}`, 
    fetcher
  );

  return (
    <LiveIntegrationsTable initialData={data} />
  );
}

export default function LiveIntegrationsPage() {
  return (
    <div className="min-h-screen">
      <Header />
      <div className="px-4 py-6">
        <h1 className="text-2xl font-bold mb-8">Live Integrations</h1>
        
        <Card>
          <CardHeader>
            <CardTitle>Live Integrations</CardTitle>
          </CardHeader>
          <CardContent>
            <Suspense fallback={<div>Loading...</div>}>
              <LiveIntegrationsContent />
            </Suspense>
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 