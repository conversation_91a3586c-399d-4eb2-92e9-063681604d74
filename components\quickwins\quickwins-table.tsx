"use client";

import { QuickWin, IntegrationType, QuickWinStatus } from "@/types";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { getIntegrationStatusBgColor } from "@/lib/format-utils";
import { 
  formatDate as formatDateUtil, 
  calculateWeeksElapsed,
  isIntegrationDelayed
} from "@/lib/date-utils";
import { TablePagination } from "@/components/ui/table-pagination";
import { useCallback, useState, useEffect, useMemo } from "react";
import useSWR from 'swr';
import { EditIntegrationDialog } from "./edit-quickwin-dialog";
import { Input } from "@/components/ui/input";
import { Search, Filter, Download } from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface QuickWinsTableProps {
  quickwins: QuickWin[]; 
  currentPage: number;
  onPageChange: (page: number) => void;
  onFilteredDataChange?: (data: QuickWin[]) => void;
  // Updated prop name from 'quickwins' to 'data'
}

interface Filters {
  batch?: string;
  type?: IntegrationType;
  status?: QuickWinStatus;
  country?: string;
  manager?: string;
}

const fetcher = (url: string) => fetch(url).then(res => res.json());

const integrationTypes: IntegrationType[] = ['B2B', 'B2C', 'PP', 'Meta'];
const quickwinStatuses: QuickWinStatus[] = [
  'Initiation',
  'Onboarding',
  'Live',
  'Hold'
];

const ALL_VALUE = "all";

// Use the utility function for status colors
const getStatusColor = getIntegrationStatusBgColor;

const handleExport = async () => {
  try {
    const response = await fetch('/api/quickwins/export');
    if (!response.ok) throw new Error('Export failed');
    
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'quickwins.xlsx';
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  } catch (error) {
    console.error('Error exporting quickwins:', error);
  }
};

export function QuickWinsTable({ 
  currentPage, 
  onPageChange,
  onFilteredDataChange,
  quickwins
}: QuickWinsTableProps) {
  const [selectedIntegration, setSelectedIntegration] = useState<QuickWin | null>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeSearch, setActiveSearch] = useState('');
  const [filters, setFilters] = useState<Filters>({});
  const pageSize = 25;
  
  const { data: swrData, error, isLoading } = useSWR<{
    data: QuickWin[];
    total: number;
    totalPages: number;
  }>(
    `/api/quickwins?page=${currentPage}&pageSize=${pageSize}&search=${encodeURIComponent(activeSearch)}`,
    fetcher,
    {
      keepPreviousData: true
    }
  );
  const totalPages = swrData?.totalPages || 0;


  const handleSearch = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      setActiveSearch(searchTerm);
      onPageChange(1);
    }
  };

  const handleRowClick = (integration: QuickWin) => {
    setSelectedIntegration(integration);
    setEditDialogOpen(true);
  };
  // Use the utility function for checking delayed integrations
  const isDelayed = isIntegrationDelayed;
  // Use the utility function for date formatting
  const formatDate = formatDateUtil;

  const handleFilterChange = (key: keyof Filters, value: string | null) => {
    setFilters(prev => {
      const newFilters = {
        ...prev,
        [key]: value === ALL_VALUE ? undefined : value
      };
      return newFilters;
    });
    onPageChange(1);
  };

  const filteredData = useMemo(() => {
    if (!swrData?.data) return [];
    return swrData.data.filter((integration: QuickWin) => {
      if (filters.batch && integration.batch !== filters.batch) return false;
      if (filters.type && integration.type !== filters.type) return false;
      if (filters.status && integration.status !== filters.status) return false;
      if (filters.country && integration.country !== filters.country) return false;
      if (filters.manager && integration.manager !== filters.manager) return false;
      return true;
    });
  }, [swrData?.data, filters]);

  useEffect(() => {
    if (onFilteredDataChange) {
      onFilteredDataChange(filteredData);
    }
  }, [filteredData, onFilteredDataChange]);

  const handlePageChange = useCallback((newPage: number) => {
    onPageChange(newPage); // Update currentPage in the parent
  }, [onPageChange]);

  const uniqueBatches = useMemo<string[]>(() => 
    Array.from(new Set((swrData?.data?.map((i: QuickWin) => i.batch) || []) as string[])).sort(),
    [swrData?.data]
  );

  const uniqueCountries = useMemo<string[]>(() => 
      Array.from(new Set((swrData?.data?.map((i: QuickWin) => i.country) || []) as string[])).sort(),
      [swrData?.data]
    );

  const uniqueManagers = useMemo<string[]>(() => 
      Array.from(new Set((swrData?.data?.map((i: QuickWin) => i.manager) || []) as string[])).sort(),
      [swrData?.data]
    );
  const totalFilteredPages = totalPages;
  // Pagination is now handled by the TablePagination component

  if (isLoading) {
    return <div className="text-center py-4">Loading...</div>;
  }

  if (error) {
    return <div className="text-center py-4 text-red-500">Error loading data</div>;
  }

  if (!quickwins || !Array.isArray(quickwins)) {
    return (
      <div className="flex items-center justify-between pb-4">
        <div className="flex items-center space-x-2">
          <Button onClick={handleExport}>
            <Download className="h-4 w-4" />
            Export
          </Button>
        </div>
        <div className="p-4 text-center text-gray-500">
          No integration data available
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-3 h-4 w-4" />
          <Input
            placeholder="Search integrations... (Press Enter to search)"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onKeyDown={handleSearch}
            className="pl-10 w-full"
          />
        </div>
        <div className="flex items-center space-x-2 shrink-0">
          <Button
            onClick={handleExport}
            className="flex items-center space-x-2"
            variant="outline"
          >
            <Download className="h-4 w-4" />
            <span>Export</span>
          </Button>
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="flex items-center space-x-2">
                <Filter className="h-4 w-4" />
                <span>Filter</span>
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80">
              <div className="grid gap-4">
                <div className="space-y-2">
                  <h4 className="font-medium leading-none">Filters</h4>
                  <p className="text-sm text-muted-foreground">
                    Filter quickwins by various criteria
                  </p>
                </div>
                <div className="grid gap-2">
                  <div className="grid grid-cols-3 items-center gap-4">
                    <Label htmlFor="type">Type</Label>
                    <Select
                      value={filters.type || 'all'}
                      onValueChange={(value) => handleFilterChange('type', value === 'all' ? null : value)}
                    >
                      <SelectTrigger className="col-span-2">
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All</SelectItem>
                        {integrationTypes.map((type) => (
                          <SelectItem key={type} value={type}>
                            {type}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="grid grid-cols-3 items-center gap-4">
                    <Label htmlFor="status">Status</Label>
                    <Select
                      value={filters.status || 'all'}
                      onValueChange={(value) => handleFilterChange('status', value === 'all' ? null : value)}
                    >
                      <SelectTrigger className="col-span-2">
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All</SelectItem>
                        {quickwinStatuses.map((status) => (
                          <SelectItem key={status} value={status}>
                            {status}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="grid grid-cols-3 items-center gap-4">
                    <Label htmlFor="manager">Manager</Label>
                    <Select
                      value={filters.manager || 'all'}
                      onValueChange={(value) => handleFilterChange('manager', value === 'all' ? null : value)}
                    >
                      <SelectTrigger className="col-span-2">
                        <SelectValue placeholder="Select manager" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All</SelectItem>
                        {uniqueManagers.map((manager) => (
                          <SelectItem key={manager} value={manager}>
                            {manager}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </div>
      
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="font-semibold">Batch</TableHead>
              <TableHead className="font-semibold">OTA</TableHead>
              <TableHead className="font-semibold">Type</TableHead>
              <TableHead className="font-semibold">Country</TableHead>
              <TableHead className="font-semibold">FOP</TableHead>
              <TableHead className="font-semibold">IATA</TableHead>
              <TableHead className="font-semibold">True IATA</TableHead>
              <TableHead className="font-semibold">Start Date</TableHead>
              <TableHead className="font-semibold">Progress</TableHead>
              <TableHead className="font-semibold">Status</TableHead>
              <TableHead className="font-semibold">Last Comm.</TableHead>
              <TableHead className="font-semibold">Expected End</TableHead>
              <TableHead className="font-semibold">Manager</TableHead>
              <TableHead className="font-semibold">Actual End</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredData.map((integration: QuickWin, rowIndex: number) => {
              const weeksElapsed: number = calculateWeeksElapsed(
                integration.startDate, 
                integration.actualEndDate as Date | string | null
              );

              return (
              <TableRow
                key={integration._id?.toString() || rowIndex}
                className={`${
                  isDelayed({ ...integration, weeksElapsed }) ? "text-red-800" : ""
                } cursor-pointer hover:bg-muted`}
                onClick={() => handleRowClick(integration)}
              >
                <TableCell className="font-medium">{integration.batch}</TableCell>
                <TableCell>{integration.ota}</TableCell>
                <TableCell>{integration.type}</TableCell>
                <TableCell>{integration.country}</TableCell>
                <TableCell>{integration.fop}</TableCell>
                <TableCell>{integration.iata}</TableCell>
                <TableCell>{integration.trueIata}</TableCell>
                <TableCell>{formatDate(integration.startDate)}</TableCell>
                <TableCell>
                  {weeksElapsed.toFixed(1)} of {integration.weeks} weeks
                </TableCell>
                <TableCell>
                  <Badge
                    className={`${getStatusColor(integration.status)} text-white`}
                  >
                    {integration.status}
                  </Badge>
                </TableCell>
                <TableCell>{formatDate(integration.lastCommDate)}</TableCell>
                <TableCell>{formatDate(integration.expectedEndDate)}</TableCell>
                <TableCell>{integration.manager}</TableCell>
                <TableCell>{formatDate(integration.actualEndDate)}</TableCell>
              </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>

      {filteredData.length > 0 && (
        <TablePagination
          currentPage={currentPage}
          totalPages={totalFilteredPages}
          onPageChange={handlePageChange}
          maxVisiblePages={3}
          totalItems={filteredData.length}
          pageSize={pageSize}
        />
      )}

      <EditIntegrationDialog
        integration={selectedIntegration}
        open={editDialogOpen}
        onOpenChange={setEditDialogOpen}
      />
    </div>
  );
}