import { NextResponse } from 'next/server';
import { connectDB, Integration, MasterOTA } from '@/lib/mongodb';

export const runtime = 'nodejs';

/**
 * POST endpoint specifically for archiving integrations
 * Only updates the archived flag and archivedDate
 */
export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB();
    
    console.log('Archiving integration with ID:', params.id);
    
    // First, get the integration to get its masterOtaId, type, and actualEndDate
    const integration = await Integration.findById(params.id);
    if (!integration) {
      return NextResponse.json({ error: 'Integration not found' }, { status: 404 });
    }

    // Get current date for archive timestamp
    const archivedDate = new Date();
    
    // Update only the archive-related fields
    const result = await Integration.updateOne(
      { _id: params.id },
      { 
        $set: { 
          archived: true,
          archivedDate: archivedDate
        } 
      }
    );
    
    console.log('Archive result:', result);
    
    if (!result.acknowledged || result.matchedCount === 0) {
      return NextResponse.json({ error: 'Integration not found' }, { status: 404 });
    }
    
    if (result.modifiedCount === 0) {
      return NextResponse.json({ 
        warning: 'Integration found but not modified. Perhaps it is already archived?',
        success: false
      }, { status: 200 });
    }

    // If the integration has a masterOtaId, update its usage data
    if (integration.masterOtaId) {
      // Map integration type to usage field
      const integrationType = integration.type?.toLowerCase() || 'booking';
      
      // Create update object for master OTA
      // Set the entire integration type object structure
      const usageUpdate = {
        [`${integrationType}`]: {
          live: true,
          liveDate: integration.actualEndDate || archivedDate,
          lastUsedDate: null
        }
      };

      // Update the master OTA
      await MasterOTA.updateOne(
        { _id: integration.masterOtaId },
        { $set: usageUpdate }
      );
    }
    
    // Fetch the updated document
    const updatedDoc = await Integration.findById(params.id);
    
    return NextResponse.json({ 
      success: true, 
      message: 'Integration archived successfully',
      data: updatedDoc
    });
  } catch (error) {
    console.error('Error archiving integration:', error);
    return NextResponse.json({ error: 'Failed to archive integration' }, { status: 500 });
  }
} 