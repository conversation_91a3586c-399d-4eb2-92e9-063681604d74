import { NextResponse } from 'next/server';
import { connectDB, Integration, QuickWin, MasterOTA } from '@/lib/mongodb';

interface StatusCounts {
  [key: string]: number;
}

interface AllStatusCounts {
  Integration: StatusCounts;
  QuickWin: StatusCounts;
}

interface StatusCount {
  _id: string;
  count: number;
}

export async function GET() {
  try {
    await connectDB();

    // Count MasterOTAs with at least one usage feature enabled
    const usageCount = await MasterOTA.countDocuments({
      $or: [
        { booking: true },
        { ancillaries: true },
        { cancel: true },
        { void: true },
        { change: true },
        { interline: true },
        { multiCity: true },
        { apis: true }
      ]
    });

    // Get counts from MongoDB
    const counts = {
      Integration: await Integration.countDocuments({ archived: { $ne: true } }),
      QuickWin: await QuickWin.countDocuments({ archived: { $ne: true } }),
      Usage: usageCount
    };

    // Get status counts for Integration
    const integrationStatusCounts: StatusCount[] = await Integration.aggregate([
      {
        $match: { archived: { $ne: true } }
      },
      {
        $group: {
          _id: "$status",
          count: { $sum: 1 }
        }
      }
    ]);

    // Get status counts for QuickWin
    const quickWinStatusCounts: StatusCount[] = await QuickWin.aggregate([
      {
        $match: { archived: { $ne: true } }
      },
      {
        $group: {
          _id: "$status",
          count: { $sum: 1 }
        }
      }
    ]);

    // Format status counts
    const statusCounts: AllStatusCounts = {
      Integration: {},
      QuickWin: {}
    };

    integrationStatusCounts.forEach(({ _id, count }) => {
      statusCounts.Integration[_id] = count;
    });

    quickWinStatusCounts.forEach(({ _id, count }) => {
      statusCounts.QuickWin[_id] = count;
    });

    return NextResponse.json({ counts, statusCounts });
  } catch (error) {
    console.error('Error fetching stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch stats' },
      { status: 500 }
    );
  }
} 