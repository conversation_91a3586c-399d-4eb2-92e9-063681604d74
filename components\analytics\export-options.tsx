'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Download } from 'lucide-react';

export function ExportOptions() {
  const [exportType, setExportType] = useState('month');
  const [month, setMonth] = useState('');
  const [year, setYear] = useState(new Date().getFullYear().toString());
  const [ota, setOta] = useState('');
  const [isExporting, setIsExporting] = useState(false);

  // Generate array of years (current year and 2 years back)
  const currentYear = new Date().getFullYear();
  const years = [currentYear, currentYear - 1, currentYear - 2];

  const handleExport = async () => {
    setIsExporting(true);
    try {
      let url = '/api/analytics-mongo/export?';
      
      if (exportType === 'month' && month && year) {
        url += `month=${month}&year=${year}`;
      } else if (exportType === 'ota' && ota) {
        url += `ota=${encodeURIComponent(ota)}`;
      }
      
      // Open the URL in a new tab
      window.open(url, '_blank');
    } catch (error) {
      console.error('Error exporting data:', error);
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Export Analytics Data</CardTitle>
        <CardDescription>
          Export analytics data filtered by month or OTA
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="month" onValueChange={setExportType}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="month">By Month</TabsTrigger>
            <TabsTrigger value="ota">By OTA</TabsTrigger>
          </TabsList>
          
          <TabsContent value="month" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="month">Month</Label>
                <Select value={month} onValueChange={setMonth}>
                  <SelectTrigger id="month">
                    <SelectValue placeholder="Select month" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="01">January</SelectItem>
                    <SelectItem value="02">February</SelectItem>
                    <SelectItem value="03">March</SelectItem>
                    <SelectItem value="04">April</SelectItem>
                    <SelectItem value="05">May</SelectItem>
                    <SelectItem value="06">June</SelectItem>
                    <SelectItem value="07">July</SelectItem>
                    <SelectItem value="08">August</SelectItem>
                    <SelectItem value="09">September</SelectItem>
                    <SelectItem value="10">October</SelectItem>
                    <SelectItem value="11">November</SelectItem>
                    <SelectItem value="12">December</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="year">Year</Label>
                <Select value={year} onValueChange={setYear}>
                  <SelectTrigger id="year">
                    <SelectValue placeholder="Select year" />
                  </SelectTrigger>
                  <SelectContent>
                    {years.map((y) => (
                      <SelectItem key={y} value={y.toString()}>
                        {y}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="ota" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="ota">OTA Name</Label>
              <Input
                id="ota"
                placeholder="Enter OTA name"
                value={ota}
                onChange={(e) => setOta(e.target.value)}
              />
            </div>
          </TabsContent>
          
          <div className="mt-4">
            <Button 
              onClick={handleExport} 
              disabled={
                (exportType === 'month' && (!month || !year)) || 
                (exportType === 'ota' && !ota) ||
                isExporting
              }
              className="w-full"
            >
              <Download className="mr-2 h-4 w-4" />
              {isExporting ? 'Exporting...' : 'Export Data'}
            </Button>
          </div>
        </Tabs>
      </CardContent>
    </Card>
  );
}
