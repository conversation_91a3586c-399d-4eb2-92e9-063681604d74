import { NextResponse } from 'next/server';
import { connectDB, Integration } from '@/lib/mongodb';
import { ObjectId } from 'mongodb';

export const runtime = 'nodejs';

export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB();
    const data = await request.json();
    
    // Log the incoming data for debugging
    console.log('Integration update request received:');
    console.log('ID:', params.id);
    console.log('Data:', JSON.stringify(data, null, 2));
    console.log('archived:', data.archived);
    console.log('archivedDate:', data.archivedDate);

    // Fetch the existing integration
    const existingIntegration = await Integration.findById(params.id);
    if (!existingIntegration) {
      return NextResponse.json({ error: 'Integration not found' }, { status: 404 });
    }

    const startDate = data.startDate ? new Date(data.startDate) : existingIntegration.startDate;
    const actualEndDate = data.actualEndDate ? new Date(data.actualEndDate) : existingIntegration.actualEndDate;
    const currentDate = new Date();

    // Calculate weeksElapsed dynamically if not provided
    const endDate = actualEndDate || currentDate;
    const timeDifference = endDate.getTime() - new Date(startDate).getTime();
    const weeksElapsed = data.weeksElapsed ?? Math.max(0, Math.round((timeDifference / (1000 * 60 * 60 * 24 * 7)) * 10) / 10);

    // Create update document with only the fields in our schema
    const updateDoc = {
      batch: data.batch,
      ota: data.ota,
      masterOtaId: data.masterOtaId,
      weeks: data.weeks,
      weeksElapsed,
      status: data.status,
      manager: data.manager,
      startDate: startDate,
      lastCommDate: data.lastCommDate ? new Date(data.lastCommDate) : existingIntegration.lastCommDate,
      actualEndDate: actualEndDate || null,
      comments: data.comments || '',
      archived: data.archived !== undefined ? Boolean(data.archived) : existingIntegration.archived,
      archivedDate: data.archivedDate ? new Date(data.archivedDate) : 
                    (data.archived && !existingIntegration.archived) ? new Date() : 
                    existingIntegration.archivedDate
    };
    
    // Update the integration using proper ObjectId conversion
    const updateResult = await Integration.findByIdAndUpdate(
      params.id,
      { $set: updateDoc },
      { new: true }
    );

    console.log('Update result:', updateResult);

    // If update succeeded, return the updated document
    if (updateResult) {
      return NextResponse.json({ success: true, data: updateResult });
    }

    return NextResponse.json({ error: 'Integration not found' }, { status: 404 });
  } catch (error) {
    console.error('Error updating integration:', error);
    return NextResponse.json({ error: 'Failed to update integration' }, { status: 500 });
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB();
    const deletedIntegration = await Integration.findByIdAndDelete(params.id);

    if (deletedIntegration) {
      return NextResponse.json({ success: true });
    }

    return NextResponse.json({ error: 'Integration not found' }, { status: 404 });
  } catch (error) {
    console.error('Error deleting integration:', error);
    return NextResponse.json({ error: 'Failed to delete integration' }, { status: 500 });
  }
} 