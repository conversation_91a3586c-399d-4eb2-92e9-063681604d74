import { NextResponse } from "next/server";
import { connectDB, Books } from "../../../../lib/mongodb";

export const runtime = "nodejs";
export const dynamic = 'force-dynamic';

// Helper function to format date for display
const formatDate = (date: Date): string => {
  const day = date.getDate().toString().padStart(2, '0');
  const month = date.toLocaleString('en-US', { month: 'short' });
  const year = date.getFullYear().toString().substring(2);
  return `${day}-${month}-${year}`;
};

export async function GET(request: Request) {
  try {
    const searchParams = new URL(request.url).searchParams;
    const search = searchParams.get("search") || "";
    
    // Connect to MongoDB
    await connectDB();
    
    // Create base query conditions
    const baseQuery = search ? {
      $or: [
        { otaName: { $regex: search, $options: 'i' } },
        ...(search.match(/^\d{1,2}[-\/]\w+[-\/]\d{2,4}$/) ? [{
          date: {
            $gte: new Date(search),
            $lt: new Date(new Date(search).setDate(new Date(search).getDate() + 1))
          }
        }] : [])
      ]
    } : {};

    // Check if it's an OTA search
    const isOtaSearch = search && !search.match(/^\d{1,2}[-\/]\w+[-\/]\d{2,4}$/);

    // Use aggregation pipeline for efficient data processing
    const aggregationPipeline = [
      { $match: baseQuery },
      {
        $group: {
          _id: {
            date: "$date",
            ...(isOtaSearch ? { otaName: "$otaName" } : {})
          },
          BookingCounts: { $sum: { $ifNull: ["$bookingCount", 0] } }
        }
      },
      {
        $project: {
          _id: 0,
          date: "$_id.date",
          ...(isOtaSearch ? { OTAName: "$_id.otaName" } : {}),
          BookingCounts: 1
        }
      },
      { $sort: { date: 1 as const } }
    ];

    const response = await Books.aggregate(aggregationPipeline);
    
    return NextResponse.json({ 
      success: true, 
      data: response,
      searchTerm: search || undefined
    });
  } catch (error) {
    console.error("Error fetching daily books data from MongoDB:", error);
    return NextResponse.json({ success: false, error: (error as Error).message }, { status: 500 });
  }
} 