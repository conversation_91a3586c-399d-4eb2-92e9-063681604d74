'use client';

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowDown, BarChart3 } from "lucide-react";
import { formatLargeNumber, formatOTAName } from '@/lib/format-utils';
import { cn } from "@/lib/utils";

type OTAData = {
  OTAName: string;
  TotalLooks: number;
  TotalBookings: number;
  LookToBook: number;
  BookingPercentage: number;
};

export default function BottomOTAsCard() {
  const [data, setData] = useState<OTAData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchBottomOTAs = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/analytics-mongo/bottom-otas');
        const result = await response.json();
        
        if (!result.success) {
          throw new Error(result.error || 'Failed to fetch bottom OTAs');
        }
        
        setData(result.data);
      } catch (err) {
        console.error('Error fetching bottom OTAs:', err);
        setError('Failed to load bottom OTAs data');
      } finally {
        setLoading(false);
      }
    };

    fetchBottomOTAs();
  }, []);  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-blue-900">Zero Booking OTAs</CardTitle>
            <CardDescription>Ranked by highest search volumes (ascending)</CardDescription>
          </div>
          <BarChart3 className="h-4 w-4 text-blue-500" />
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex justify-center items-center h-48">
            <p className="text-sm text-muted-foreground">Loading...</p>
          </div>
        ) : error ? (
          <div className="flex justify-center items-center h-48">
            <p className="text-sm text-red-500">{error}</p>
          </div>
        ) : data.length === 0 ? (
          <div className="flex justify-center items-center h-48">
            <p className="text-sm text-muted-foreground">No OTA data available</p>
          </div>
        ) : (
          <div className="space-y-2 mt-2">
            {data.map((ota, index) => (
              <div key={index} className="pb-1.5 border-b border-dashed last:border-0 last:pb-0">
                <div className="flex justify-between items-center">
                  <div className="flex items-center space-x-2">
                    <div className={cn(
                      "font-medium truncate",
                      index === 0 && "text-blue-900",
                      index === 1 && "text-blue-800",                      index === 2 && "text-blue-700"
                    )} title={ota.OTAName}>{formatOTAName(ota.OTAName)}</div>
                    {index === 0 && (
                      <div className="bg-blue-100 text-blue-800 text-xs px-1.5 py-0.5 rounded flex items-center">
                        <ArrowDown className="h-3 w-3 mr-0.5" />
                        Zero
                      </div>
                    )}
                  </div>
                  <div className="text-sm">
                    <span className={cn(
                      "font-medium",
                      index === 0 && "text-blue-900",
                      index === 1 && "text-blue-800",
                      index === 2 && "text-blue-700"
                    )}>{formatLargeNumber(ota.TotalBookings)}</span>
                    <span className="text-muted-foreground text-xs ml-1">bookings</span>
                  </div>
                </div>
                <div className="flex justify-between items-center text-xs text-muted-foreground mt-0.5">
                  <div>
                    {formatLargeNumber(ota.TotalLooks)} looks
                    {ota.TotalBookings > 0 && (
                      <>
                        <span className="mx-1">•</span>
                        1:{ota.LookToBook} ratio
                      </>
                    )}
                  </div>
                  <div className={cn(
                    index === 0 && "text-blue-600",
                    index === 1 && "text-blue-500",
                    index === 2 && "text-blue-400"
                  )}>{ota.BookingPercentage}%</div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
