'use client';

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowUp, BarChart3 } from "lucide-react";
import { formatLargeNumber, formatOTAName } from '@/lib/format-utils';
import { cn } from "@/lib/utils";

type OTAData = {
  OTAName: string;
  TotalLooks: number;
  TotalBookings: number;
  LookToBook: number;
  BookingPercentage: number;
};

export default function TopOTAsCard() {
  const [data, setData] = useState<OTAData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTopOTAs = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/analytics-mongo/top-otas');
        const result = await response.json();
        
        if (!result.success) {
          throw new Error(result.error || 'Failed to fetch top OTAs');
        }
        
        setData(result.data);
      } catch (err) {
        console.error('Error fetching top OTAs:', err);
        setError('Failed to load top OTAs data');
      } finally {
        setLoading(false);
      }
    };

    fetchTopOTAs();
  }, []);

  // Find the maximum booking count to calculate percentages
  const maxBookings = data.length > 0 
    ? Math.max(...data.map(ota => ota.TotalBookings))
    : 0;  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-green-900">Top 25 OTAs by Bookings</CardTitle>
            <CardDescription>Ranked by total booking volume</CardDescription>
          </div>
          <BarChart3 className="h-4 w-4 text-green-500" />
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex justify-center items-center h-48">
            <p className="text-sm text-muted-foreground">Loading...</p>
          </div>
        ) : error ? (
          <div className="flex justify-center items-center h-48">
            <p className="text-sm text-red-500">{error}</p>
          </div>
        ) : data.length === 0 ? (
          <div className="flex justify-center items-center h-48">
            <p className="text-sm text-muted-foreground">No OTA data available</p>
          </div>
        ) : (
          <div className="space-y-2 mt-2">
            {data.map((ota, index) => (
              <div key={index} className="pb-1.5 border-b border-dashed last:border-0 last:pb-0">
                <div className="flex justify-between items-center">
                  <div className="flex items-center space-x-2">
                    <div className={cn(
                      "font-medium truncate",
                      index === 0 && "text-green-900",
                      index === 1 && "text-green-800",                      index === 2 && "text-green-700"
                    )} title={ota.OTAName}>{formatOTAName(ota.OTAName)}</div>
                    {index === 0 && (
                      <div className="bg-green-100 text-green-800 text-xs px-1.5 py-0.5 rounded flex items-center">
                        <ArrowUp className="h-3 w-3 mr-0.5" />
                        Top
                      </div>
                    )}
                  </div>
                  <div className="text-sm">
                    <span className={cn(
                      "font-medium",
                      index === 0 && "text-green-900",
                      index === 1 && "text-green-800",
                      index === 2 && "text-green-700"
                    )}>{formatLargeNumber(ota.TotalBookings)}</span>
                    <span className="text-muted-foreground text-xs ml-1">bookings</span>
                  </div>
                </div>
                <div className="flex justify-between items-center text-xs text-muted-foreground mt-0.5">
                  <div>
                    {formatLargeNumber(ota.TotalLooks)} looks
                    <span className="mx-1">•</span>
                    1:{ota.LookToBook} ratio
                  </div>
                  <div className={cn(
                    index === 0 && "text-green-600",
                    index === 1 && "text-green-500",
                    index === 2 && "text-green-400"
                  )}>{ota.BookingPercentage}%</div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}