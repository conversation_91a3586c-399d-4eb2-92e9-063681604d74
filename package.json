{"name": "nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev ", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^3.3.4", "@next/swc-wasm-nodejs": "14.1.3", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-aspect-ratio": "^1.0.3", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-context-menu": "^2.1.5", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-hover-card": "^1.0.7", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-menubar": "^1.0.4", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-toggle-group": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@types/react": "18.2.64", "@types/react-dom": "18.2.21", "autoprefixer": "10.4.18", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "cmdk": "^0.2.1", "csv-parser": "^3.2.0", "date-fns": "^3.3.1", "dotenv": "^16.5.0", "embla-carousel-react": "^8.0.0", "input-otp": "^1.1.0", "lucide-react": "^0.350.0", "next": "14.1.3", "next-auth": "^4.24.7", "next-themes": "^0.2.1", "oidc-client-ts": "^3.0.1", "postcss": "8.4.35", "react": "18.2.0", "react-day-picker": "^8.10.0", "react-dom": "18.2.0", "react-hook-form": "^7.51.0", "react-resizable-panels": "^2.0.12", "recharts": "^2.12.2", "sequelize": "^6.37.1", "sonner": "^1.4.3", "sqlite3": "^5.1.7", "swr": "^2.2.5", "tailwind-merge": "^2.2.1", "tailwindcss": "3.4.1", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.0", "xlsx": "^0.18.5", "zod": "^3.22.4"}, "devDependencies": {"@types/mongoose": "^5.11.96", "@types/node": "^22.15.3", "@types/sequelize": "^4.28.20", "@types/sqlite3": "^3.1.11", "https-localhost": "^4.7.1", "ts-node": "^10.9.2", "tsx": "^4.19.3", "typescript": "^5.8.3"}}