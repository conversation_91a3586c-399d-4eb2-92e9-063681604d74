import { NextResponse } from 'next/server';
import { connectDB, MasterOTA } from '@/lib/mongodb';
import mongoose from 'mongoose';

export const runtime = 'nodejs';

/**
 * POST endpoint to update integration status and live date for a master OTA
 */
export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB();
    
    const { integrationType } = await request.json();
    
    if (!integrationType) {
      return NextResponse.json({ error: 'Integration type is required' }, { status: 400 });
    }

    // Validate and convert the ID to ObjectId
    let objectId;
    try {
      objectId = new mongoose.Types.ObjectId(params.id);
    } catch (error) {
      return NextResponse.json({ error: 'Invalid ID format' }, { status: 400 });
    }
    
    console.log('Updating integration status for master OTA with ID:', objectId, 'Type:', integrationType);

    // First, ensure the integration type object exists with default values if it doesn't exist
    const masterOta = await MasterOTA.findById(objectId);
    if (!masterOta) {
      return NextResponse.json({ error: 'Master OTA not found' }, { status: 404 });
    }

    // Initialize the integration type if it doesn't exist
    if (!masterOta[integrationType]) {
      masterOta[integrationType] = {
        live: false,
        liveDate: null,
        lastUsedDate: null
      };
    }

    // Update the integration type status
    masterOta[integrationType] = {
      live: true,
      liveDate: new Date(),
      lastUsedDate: new Date()
    };

    // Save the updated document
    await masterOta.save();
    
    return NextResponse.json({ 
      success: true, 
      message: `Master OTA ${integrationType} status updated successfully`
    });
  } catch (error) {
    console.error('Error updating master OTA integration status:', error);
    return NextResponse.json({ error: 'Failed to update master OTA integration status' }, { status: 500 });
  }
} 