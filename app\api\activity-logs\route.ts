import { NextRequest, NextResponse } from 'next/server';
import { connectDB, ActivityLog } from '@/lib/mongodb';

export async function GET() {
  try {
    await connectDB();
    const activityLogs = await ActivityLog.find({}).sort({ timestamp: -1 }).limit(100);
    return NextResponse.json(activityLogs);
  } catch (error) {
    return NextResponse.json({ error: 'Failed to fetch activity logs' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectDB();
    const data = await request.json();
    
    // Validate required fields
    if (!data.action || !data.details || !data.performedBy) {
      return NextResponse.json(
        { error: 'Missing required fields: action, details, or performedBy' },
        { status: 400 }
      );
    }
    
    const activityLog = await ActivityLog.create({
      action: data.action,
      details: data.details,
      performedBy: data.performedBy,
      timestamp: data.timestamp || new Date()
    });
    
    return NextResponse.json(activityLog, { status: 201 });
  } catch (error) {
    console.error('Error creating activity log:', error);
    return NextResponse.json({ error: 'Failed to create activity log' }, { status: 500 });
  }
} 