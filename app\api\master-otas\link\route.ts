import { NextResponse } from 'next/server';
import { connectDB, MasterOTA, Integration, QuickWin, SubIntegration, LiveIntegration, ActivityLog } from '@/lib/mongodb';
import mongoose from 'mongoose';

// POST to link entities to a Master OTA
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { masterOtaId, entityType, entityIds } = body;

    if (!masterOtaId || !entityType || !entityIds || !Array.isArray(entityIds)) {
      return NextResponse.json(
        { error: 'Invalid request. Required fields: masterOtaId, entityType, entityIds (array)' },
        { status: 400 }
      );
    }

    await connectDB();
    
    // Verify master OTA exists
    const masterOta = await MasterOTA.findById(masterOtaId);
    if (!masterOta) {
      return NextResponse.json({ error: 'Master OTA not found' }, { status: 404 });
    }

    // Convert string IDs to ObjectIds
    const objectIds = entityIds.map((id: string) => new mongoose.Types.ObjectId(id));
    let model;
    
    // Select model based on entity type
    switch (entityType) {
      case 'Integration':
        model = Integration;
        break;
      case 'QuickWin':
        model = QuickWin;
        break;
      case 'SubIntegration':
        model = SubIntegration;
        break;
      case 'LiveIntegration':
        model = LiveIntegration;
        break;
      default:
        return NextResponse.json({ error: 'Invalid entity type' }, { status: 400 });
    }

    // Update entities with masterOtaId
    const result = await model.updateMany(
      { _id: { $in: objectIds } },
      { masterOtaId: masterOtaId }
    );

    // Log activity
    await ActivityLog.create({
      action: 'Link to Master OTA',
      details: `Linked ${result.modifiedCount} ${entityType}(s) to master OTA: ${masterOta.otaName}`,
      performedBy: 'System', // Ideally this would be the current user
      timestamp: new Date()
    });

    return NextResponse.json({
      message: `Successfully linked ${result.modifiedCount} ${entityType}(s) to Master OTA`,
      masterOta: masterOta,
      modifiedCount: result.modifiedCount
    }, { status: 200 });
  } catch (error) {
    console.error('Error linking to master OTA:', error);
    return NextResponse.json({ error: 'Failed to link entities to master OTA' }, { status: 500 });
  }
} 