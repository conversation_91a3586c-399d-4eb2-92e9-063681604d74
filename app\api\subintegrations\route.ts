import { NextRequest, NextResponse } from 'next/server';
import { connectDB, SubIntegration } from '@/lib/mongodb';
import mongoose from 'mongoose';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const pageSize = parseInt(searchParams.get('pageSize') || '25');
    const search = searchParams.get('search') || '';
    const skip = (page - 1) * pageSize;
    
    await connectDB();
    
    // Create aggregation pipeline
    const pipeline: mongoose.PipelineStage[] = [];
    
    // Lookup stage for populating masterOtaId
    pipeline.push({
      $lookup: {
        from: 'masterotas', // Collection name may need to be adjusted based on your actual collection name
        localField: 'masterOtaId',
        foreignField: '_id',
        as: 'masterOtaData'
      }
    });
    
    // Unwind the array created by lookup
    pipeline.push({
      $unwind: {
        path: '$masterOtaData',
        preserveNullAndEmptyArrays: true
      }
    });
    
    // Match stage for search if a search term is provided
    if (search) {
      pipeline.push({
        $match: {
          $and: [
            {
              $or: [
                { archived: { $exists: false } },
                { archived: false }
              ]
            },
            {
              $or: [
                { batch: { $regex: search, $options: 'i' } },
                { status: { $regex: search, $options: 'i' } },
                { 'masterOtaData.otaName': { $regex: search, $options: 'i' } },
                { 'masterOtaData.country': { $regex: search, $options: 'i' } },
                { 'masterOtaData.clientId': { $regex: search, $options: 'i' } }
              ]
            }
          ]
        }
      });
    } else {
      // Add match stage for archived filter even when there's no search
      pipeline.push({
        $match: {
          $or: [
            { archived: { $exists: false } },
            { archived: false }
          ]
        }
      });
    }
    
    // Sort by startDate (descending as in the original code)
    pipeline.push({
      $sort: { startDate: -1 }
    } as mongoose.PipelineStage);
    
    // Get total count before pagination
    const countPipeline = [...pipeline];
    countPipeline.push({ $count: 'total' } as mongoose.PipelineStage);
    
    // Add pagination
    pipeline.push({ $skip: skip } as mongoose.PipelineStage);
    pipeline.push({ $limit: pageSize } as mongoose.PipelineStage);
    
    // Execute queries
    const [results, countResult] = await Promise.all([
      SubIntegration.aggregate(pipeline),
      SubIntegration.aggregate(countPipeline)
    ]);
    
    const total = countResult.length > 0 ? countResult[0].total : 0;
    const totalPages = Math.ceil(total / pageSize);
    
    // Transform data to include master OTA fields
    const transformedData = results.map(subintegration => {
      const masterOta = subintegration.masterOtaData || {};
      return {
        ...subintegration,
        masterOtaId: subintegration.masterOtaId, // Preserve the original ID reference
        masterOtaData: undefined, // Remove the expanded data to match original format
        ota: masterOta.otaName || 'N/A',
        type: masterOta.type || 'N/A',
        country: masterOta.country || 'N/A',
        iata: masterOta.iata || 'N/A',
        trueIata: masterOta.trueIata || 'N/A',
        fop: masterOta.fop || 'N/A',
        clientId: masterOta.clientId || 'N/A',
        userId: masterOta.userId || 'N/A',
        contact: masterOta.contact || 'N/A'
      };
    });
    
    return NextResponse.json({
      data: transformedData,
      total,
      totalPages,
      currentPage: page,
    });
  } catch (error) {
    console.error('Error fetching subintegrations:', error);
    return NextResponse.json(
      { error: 'Failed to fetch subintegrations' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    await connectDB();
    
    // Validate required fields
    if (!data.masterOtaId) {
      return NextResponse.json({ error: 'masterOtaId is required' }, { status: 400 });
    }
    
    // Calculate expected end date based on start date and weeks
    const startDate = new Date(data.startDate);
    const expectedEndDate = new Date(startDate);
    expectedEndDate.setDate(startDate.getDate() + (data.weeks * 7));
    
    // Calculate progress based on weeks elapsed vs total weeks
    const currentDate = new Date();
    const timeDifference = currentDate.getTime() - startDate.getTime();
    const weeksElapsed = Math.max(0, Math.round((timeDifference / (1000 * 60 * 60 * 24 * 7)) * 10) / 10);
    const progress = Math.min(100, Math.round((weeksElapsed / data.weeks) * 100));
    
    const subIntegrationData = {
      batch: data.batch,
      ota: data.ota,
      masterOtaId: data.masterOtaId,
      flow: data.flow,
      startDate,
      weeks: parseInt(data.weeks),
      progress,
      status: data.status || 'Initiation',
      expectedEndDate,
      manager: data.manager || '',
      actualEndDate: data.actualEndDate ? new Date(data.actualEndDate) : null,
      archived: false,
      archivedDate: null
    };
    
    const newSubIntegration = new SubIntegration(subIntegrationData);
    const savedSubIntegration = await newSubIntegration.save();
    
    return NextResponse.json(savedSubIntegration, { status: 201 });
  } catch (error) {
    console.error('Error creating subintegration:', error);
    return NextResponse.json(
      { error: 'Failed to create subintegration' },
      { status: 500 }
    );
  }
} 