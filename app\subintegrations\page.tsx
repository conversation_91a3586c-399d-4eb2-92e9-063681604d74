'use client';

import { useState } from 'react';
import { Head<PERSON> } from "@/components/header";
import { PageStatus } from "@/components/ui/page-status";
import { DashboardHeader } from "@/components/subintegrations/dashboard-header";
import { FlowCardsGrid } from "@/components/subintegrations/flow-cards-grid";
import { IntegrationProgressSection } from "@/components/subintegrations/integration-progress-section";
import { useSubIntegrationStats } from "@/hooks/use-subintegration-stats";

export default function SubIntegrations() {
  const [page, setPage] = useState(1);

  // Use the custom hook for stats management
  const {
    statsError,
    filteredData,
    setFilteredData,
    displayStats,
    displayFlowStats,
    isLoading
  } = useSubIntegrationStats();

  // Show loading or error states
  if (statsError || isLoading) {
    return <PageStatus isLoading={isLoading} error={statsError} />;
  }

  return (
    <div className="min-h-screen">
      <Header />
      <div className="px-4 py-6">
        <DashboardHeader displayStats={displayStats}>
          <FlowCardsGrid displayFlowStats={displayFlowStats} />
        </DashboardHeader>

        <IntegrationProgressSection
          displayStats={displayStats}
          page={page}
          setPage={setPage}
          filteredData={filteredData}
          setFilteredData={setFilteredData}
        />
      </div>
    </div>
  );
}