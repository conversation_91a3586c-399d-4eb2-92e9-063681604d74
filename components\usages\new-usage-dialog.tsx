"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Drawer,
  DrawerClose,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>er<PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ger,
  DrawerFooter,
} from "@/components/ui/drawer";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { PlusCircle } from "lucide-react";
import { useState, useEffect } from "react";
import { mutate } from "swr";
import { MasterOTA } from "@/types";
import useSWR from "swr";

interface IntegrationTypeStatus {
  live: boolean;
  liveDate: Date | null;
  lastUsedDate: Date | null;
}

interface UsageFormData {
  otaId: string;
  booking: IntegrationTypeStatus;
  ancillaries: IntegrationTypeStatus;
  seats: IntegrationTypeStatus;
  cancel: IntegrationTypeStatus;
  void: IntegrationTypeStatus;
  change: IntegrationTypeStatus;
  interline: IntegrationTypeStatus;
  multiCity: IntegrationTypeStatus;
  apis: IntegrationTypeStatus;
}

const defaultIntegrationStatus: IntegrationTypeStatus = {
  live: false,
  liveDate: null,
  lastUsedDate: null
};

const emptyUsage: UsageFormData = {
  otaId: "",
  booking: defaultIntegrationStatus,
  ancillaries: defaultIntegrationStatus,
  seats: defaultIntegrationStatus,
  cancel: defaultIntegrationStatus,
  void: defaultIntegrationStatus,
  change: defaultIntegrationStatus,
  interline: defaultIntegrationStatus,
  multiCity: defaultIntegrationStatus,
  apis: defaultIntegrationStatus,
};

const fetcher = (url: string) => fetch(url).then((res) => res.json());

export function NewUsageDialog() {
  const [open, setOpen] = useState(false);
  const [formData, setFormData] = useState<UsageFormData>(emptyUsage);
  
  // Fetch the list of master OTAs
  const { data: masterOtas = [] } = useSWR<MasterOTA[]>("/api/master-otas", fetcher);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const response = await fetch("/api/usages", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        throw new Error("Failed to create usage");
      }

      setFormData(emptyUsage);
      setOpen(false);
      mutate("/api/usages");
    } catch (error) {
      console.error("Error creating usage:", error);
    }
  };

  const handleCheckboxChange = (field: keyof Omit<UsageFormData, "otaId">) => {
    setFormData((prev) => ({
      ...prev,
      [field]: {
        ...prev[field],
        live: !prev[field].live
      }
    }));
  };

  const integrationTypes: (keyof Omit<UsageFormData, "otaId">)[] = [
    'booking',
    'ancillaries',
    'seats',
    'cancel',
    'void',
    'change',
    'interline',
    'multiCity',
    'apis'
  ];

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerTrigger asChild>
        <Button variant="outline" size="sm" className="gap-2">
          <PlusCircle className="h-4 w-4" />
          Add Module
        </Button>
      </DrawerTrigger>
      <DrawerContent>
        <div className="mx-auto w-full max-w-4xl px-6">
          <DrawerHeader>
            <DrawerTitle className="text-2xl font-semibold">Add New API Module</DrawerTitle>
          </DrawerHeader>
          
          <form onSubmit={handleSubmit} className="py-4">
            {/* Basic Information */}
            <div className="mb-5">
              <h3 className="text-base font-semibold mb-2 border-b pb-1">Basic Information</h3>
              <div className="space-y-4">
                <div className="space-y-1">
                  <Label htmlFor="otaId">OTA Name</Label>
                  <select 
                    id="otaId"
                    value={formData.otaId}
                    onChange={(e) => setFormData({ ...formData, otaId: e.target.value })}
                    required
                    className="w-full h-8 rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm"
                  >
                    <option value="">Select an OTA</option>
                    {masterOtas.map((ota) => (
                      <option key={ota._id} value={ota._id}>
                        {ota.otaName}
                      </option>
                    ))}
                  </select>
                </div>
                
                <div className="grid grid-cols-3 gap-x-8 gap-y-4">
                  {integrationTypes.map((key) => (
                    <div key={key} className="flex items-center space-x-2">
                      <Checkbox
                        id={key}
                        checked={formData[key].live}
                        onCheckedChange={() => handleCheckboxChange(key)}
                      />
                      <Label htmlFor={key}>
                        {key.charAt(0).toUpperCase() + key.slice(1)}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <DrawerFooter className="pt-2">
              <Button type="submit" disabled={!formData.otaId}>Create Module</Button>
              <DrawerClose asChild>
                <Button variant="outline">Cancel</Button>
              </DrawerClose>
            </DrawerFooter>
          </form>
        </div>
      </DrawerContent>
    </Drawer>
  );
}
