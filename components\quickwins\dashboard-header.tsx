'use client';

import { useState } from 'react';
import { ChevronsUpDown } from "lucide-react";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { Button } from "@/components/ui/button";
import { QuickWinStats } from '@/types';

interface DashboardHeaderProps {
  displayStats: QuickWinStats;
  children: React.ReactNode;
}

export function DashboardHeader({ displayStats, children }: DashboardHeaderProps) {
  const [isStatsVisible, setIsStatsVisible] = useState(true);

  return (
    <Collapsible
      open={isStatsVisible}
      onOpenChange={setIsStatsVisible}
      className="space-y-4"
    >
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center">
          <h1 className="text-2xl font-bold mr-2">Quick Wins Dashboard</h1>
          <span className="bg-primary text-primary-foreground text-sm font-medium px-2.5 py-0.5 rounded-full">
            {displayStats.total} total
          </span>
        </div>
        <CollapsibleTrigger asChild>
          <Button variant="ghost" size="sm" aria-label="Toggle Stats">
            <ChevronsUpDown className="h-5 w-5" />
            <span className="sr-only">Toggle Stats</span>
          </Button>
        </CollapsibleTrigger>
      </div>
      <CollapsibleContent>
        {children}
      </CollapsibleContent>
    </Collapsible>
  );
}
