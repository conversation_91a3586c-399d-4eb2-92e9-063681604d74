import { NextResponse } from "next/server";
import { connectDB, Looks, Books } from "../../../lib/mongodb";

export const runtime = "nodejs";
export const dynamic = 'force-dynamic';

interface LookDocument {
  date?: Date | null;
  otaName?: string;
  success?: number;
  wrongRoutes?: number;
  throttled?: number;
  failures?: number;
}

interface BookDocument {
  bookingCount?: number;
  date?: Date;
  otaName?: string;
}

interface Totals {
  Success: number;
  WrongRoutes: number;
  Throttled: number;
  Failures: number;
  Bookings: number;
  AverageLookToBook: number;
}

interface PercentageChanges {
  Success: number;
  WrongRoutes: number;
  Throttled: number;
  Failures: number;
}

// Helper function to format date for display
const formatDate = (date: Date | null | undefined): string => {
  if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
    return 'Unknown Date';
  }
  try {
    const day = date.getDate().toString().padStart(2, '0');
    const month = date.toLocaleString('en-US', { month: 'short' });
    const year = date.getFullYear().toString().slice(-2);
    return `${day}-${month}-${year}`;
  } catch (error) {
    console.warn('Error formatting date:', error);
    return 'Unknown Date';
  }
};

// Calculate totals for look data
const calculateLookTotals = (data: LookDocument[]): Omit<Totals, 'Bookings' | 'AverageLookToBook'> => {
  return data.reduce((acc, item) => ({
    Success: acc.Success + (item.success || 0),
    WrongRoutes: acc.WrongRoutes + (item.wrongRoutes || 0),
    Throttled: acc.Throttled + (item.throttled || 0),
    Failures: acc.Failures + (item.failures || 0),
  }), { Success: 0, WrongRoutes: 0, Throttled: 0, Failures: 0 });
};

// Calculate percentage change
const calculatePercentageChange = (current: number, previous: number): number => {
  if (previous === 0) {
    return 0;
  }
  const change = ((current - previous) / previous) * 100;
  return Math.max(Math.min(change, 999), -99);
};

export async function GET(request: Request) {
  try {
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get("page") || "1");
    const pageSize = parseInt(url.searchParams.get("pageSize") || "10");
    const search = url.searchParams.get("search") || "";
    const offset = (page - 1) * pageSize;

    await connectDB();

    const dateSearchMatch = search.match(/^\d{1,2}[-\/]\w+[-\/]\d{2,4}$/);
    const dateQuery = dateSearchMatch ? {
      date: {
        $gte: new Date(search),
        $lt: new Date(new Date(search).setDate(new Date(search).getDate() + 1))
      }
    } : {};

    const baseQuery = search ? {
      $or: [
        { otaName: { $regex: search, $options: 'i' } },
        ...(dateSearchMatch ? [dateQuery] : [])
      ]
    } : {};

    const totalRecords = await Looks.countDocuments(baseQuery);

    const paginatedData = await Looks.aggregate<
      LookDocument & { Bookings: number; LookToBook?: number; Date: string; OTAName: string; totalLooks: number }
    >([
      { $match: baseQuery },
      { $sort: { date: 1, otaName: 1 } },
      { $skip: offset },
      { $limit: pageSize },
      {
        $lookup: {
          from: "books",
          let: { lookDate: "$date", otaName: "$otaName" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$otaName", "$$otaName"] },
                    { $eq: ["$date", "$$lookDate"] }
                  ]
                }
              }
            }
          ],
          as: "bookings"
        }
      },
      {
        $project: {
          _id: 0,
          Date: "$date",
          OTAName: "$otaName",
          Success: { $ifNull: ["$success", 0] },
          WrongRoutes: { $ifNull: ["$wrongRoutes", 0] },
          Throttled: { $ifNull: ["$throttled", 0] },
          Failures: { $ifNull: ["$failures", 0] },
          Bookings: { $sum: "$bookings.bookingCount" },
          totalLooks: { $add: ["$success", "$wrongRoutes", "$throttled", "$failures"] }
        }
      },
      {
        $project: {
          Date: 1,
          OTAName: 1,
          Success: 1,
          WrongRoutes: 1,
          Throttled: 1,
          Failures: 1,
          Bookings: 1,
          LookToBook: {
            $cond: [
              { $gt: ["$Bookings", 0] },
              { $trunc: [{ $divide: ["$totalLooks", "$Bookings"] }] },
              0
            ]
          }
        }
      }
    ]);

    const [totalsResult, totalBookingsResult] = await Promise.all([
      Looks.aggregate<{ _id: null } & Omit<Totals, 'Bookings' | 'AverageLookToBook'>>([
        { $match: baseQuery },
        {
          $group: {
            _id: null,
            Success: { $sum: { $ifNull: ["$success", 0] } },
            WrongRoutes: { $sum: { $ifNull: ["$wrongRoutes", 0] } },
            Throttled: { $sum: { $ifNull: ["$throttled", 0] } },
            Failures: { $sum: { $ifNull: ["$failures", 0] } }
          }
        }
      ]),
      Books.aggregate<{ _id: null; total: number }>([
        { $match: baseQuery },
        { $group: { _id: null, total: { $sum: "$bookingCount" } } }
      ])
    ]);

    const totalCounts: Totals = {
      ...totalsResult[0] || { Success: 0, WrongRoutes: 0, Throttled: 0, Failures: 0 },
      Bookings: totalBookingsResult[0]?.total || 0,
      AverageLookToBook: 0, // Calculated later
    };

    const totalEvents = totalCounts.Success + totalCounts.WrongRoutes + totalCounts.Throttled + totalCounts.Failures;
    const percentages: PercentageChanges = totalEvents > 0 ? {
      Success: Number(((totalCounts.Success / totalEvents) * 100).toFixed(1)),
      WrongRoutes: Number(((totalCounts.WrongRoutes / totalEvents) * 100).toFixed(1)),
      Throttled: Number(((totalCounts.Throttled / totalEvents) * 100).toFixed(1)),
      Failures: Number(((totalCounts.Failures / totalEvents) * 100).toFixed(1))
    } : { Success: 0, WrongRoutes: 0, Throttled: 0, Failures: 0 };

    // Find the most recent date in the data
    const lastDateResult = await Looks.aggregate<{ lastDate: Date }>([
      { $match: baseQuery },
      { $sort: { date: -1 } },
      { $limit: 1 },
      { $project: { lastDate: "$date" } }
    ]);

    const lastDate = lastDateResult[0]?.lastDate || new Date();
    const lastDatePreviousWeek = new Date(lastDate);
    lastDatePreviousWeek.setDate(lastDatePreviousWeek.getDate() - 7);

    const [lastDayLooksResult, previousWeekDayLooksResult] = await Promise.all([
      Looks.aggregate<Omit<Totals, 'Bookings' | 'AverageLookToBook'>>([
        {
          $match: {
            ...baseQuery,
            date: lastDate
          }
        },
        {
          $group: {
            _id: null,
            Success: { $sum: { $ifNull: ["$success", 0] } },
            WrongRoutes: { $sum: { $ifNull: ["$wrongRoutes", 0] } },
            Throttled: { $sum: { $ifNull: ["$throttled", 0] } },
            Failures: { $sum: { $ifNull: ["$failures", 0] } }
          }
        }
      ]),
      Looks.aggregate<Omit<Totals, 'Bookings' | 'AverageLookToBook'>>([
        {
          $match: {
            ...baseQuery,
            date: lastDatePreviousWeek
          }
        },
        {
          $group: {
            _id: null,
            Success: { $sum: { $ifNull: ["$success", 0] } },
            WrongRoutes: { $sum: { $ifNull: ["$wrongRoutes", 0] } },
            Throttled: { $sum: { $ifNull: ["$throttled", 0] } },
            Failures: { $sum: { $ifNull: ["$failures", 0] } }
          }
        }
      ])
    ]);

    const lastDayTotals = lastDayLooksResult[0] || { Success: 0, WrongRoutes: 0, Throttled: 0, Failures: 0 };
    const previousWeekDayTotals = previousWeekDayLooksResult[0] || { Success: 0, WrongRoutes: 0, Throttled: 0, Failures: 0 };

    const percentageChanges: PercentageChanges = {
      Success: calculatePercentageChange(lastDayTotals.Success, previousWeekDayTotals.Success),
      WrongRoutes: calculatePercentageChange(lastDayTotals.WrongRoutes, previousWeekDayTotals.WrongRoutes),
      Throttled: calculatePercentageChange(lastDayTotals.Throttled, previousWeekDayTotals.Throttled),
      Failures: calculatePercentageChange(lastDayTotals.Failures, previousWeekDayTotals.Failures),
    };

    totalCounts.AverageLookToBook = totalCounts.Bookings > 0 ? Math.round(totalEvents / totalCounts.Bookings) : 0;

    return NextResponse.json({
      success: true,
      data: paginatedData,
      columns: ["Date", "OTAName", "Success", "WrongRoutes", "Throttled", "Failures", "Bookings", "LookToBook"],
      total: totalRecords,
      page,
      pageSize,
      totalPages: Math.ceil(totalRecords / pageSize),
      totalCounts,
      percentages,
      percentageChanges,
    });
  } catch (error) {
    console.error("Error fetching analytics data from MongoDB:", error);
    return NextResponse.json({ success: false, error: (error as Error).message }, { status: 500 });
  }
}