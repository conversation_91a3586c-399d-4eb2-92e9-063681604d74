'use client';

import { useState } from 'react';
import { Header } from "@/components/header";
import { PageStatus } from "@/components/ui/page-status";
import { DashboardHeader } from "@/components/dashboard/dashboard-header";
import { StatsCardsGrid } from "@/components/dashboard/stats-cards-grid";
import { IntegrationProgressSection } from "@/components/dashboard/integration-progress-section";
import { useIntegrationStats } from "@/hooks/use-integration-stats";

export default function Home() {
  const [page, setPage] = useState(1);

  // Use the custom hook for stats management
  const {
    statsError,
    setFilteredData,
    displayStats,
    isLoading
  } = useIntegrationStats();

  // Show loading or error states
  if (statsError || isLoading) {
    return <PageStatus isLoading={isLoading} error={statsError} />;
  }

  return (
    <div className="min-h-screen">
      <Header />
      <div className="px-4 py-6">
        <DashboardHeader displayStats={displayStats}>
          <StatsCardsGrid displayStats={displayStats} />
        </DashboardHeader>

        <IntegrationProgressSection
          displayStats={displayStats}
          page={page}
          setPage={setPage}
          setFilteredData={setFilteredData}
        />
      </div>
    </div>
  );
}