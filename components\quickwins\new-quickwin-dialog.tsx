"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>er,
  DrawerClose,
  <PERSON>er<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DrawerFooter,
} from "@/components/ui/drawer";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { PlusCircle, Calendar as CalendarIcon, Check, ChevronsUpDown } from "lucide-react";
import { useState, useEffect } from "react";
import { QuickWin, IntegrationType, QuickWinStatus, integrationTypes, quickwinStatuses, formOfPaymentOptions } from "@/types";
import { mutate } from "swr";
import { format } from "date-fns";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";

interface MasterOTA {
  _id: string;
  otaName: string;
  type: string;
  country: string;
  iata: string;
  trueIata: string;
  fop: string;
  clientId: string;
  userId: string;
  contact: string;
}

interface FormData {
  batch: string;
  ota: string;
  masterOtaId?: string;
  type?: string;
  country?: string;
  fop?: string;
  iata?: string;
  trueIata?: string;
  clientId?: string;
  userId?: string;
  contact?: string;
  weeks: string;
  manager: string;
  status: string;
  startDate: string;
  lastCommDate: string;
  actualEndDate: string;
  comments: string;
}

export function NewIntegrationDialog() {
  const [open, setOpen] = useState(false);
  const [masterOtas, setMasterOtas] = useState<MasterOTA[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedMasterOtaId, setSelectedMasterOtaId] = useState<string>('');
  const [otaSearchOpen, setOtaSearchOpen] = useState(false);
  const [formData, setFormData] = useState<FormData>({
    batch: '25_02',
    ota: '',
    weeks: '2',
    manager: '',
    status: 'Initiation',
    startDate: new Date().toISOString().split("T")[0],
    lastCommDate: '',
    actualEndDate: '',
    comments: '',
  });

  // Fetch master OTAs when the dialog opens
  useEffect(() => {
    if (open) {
      fetchMasterOtas();
    }
  }, [open]);

  const fetchMasterOtas = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/master-otas');
      if (response.ok) {
        const data = await response.json();
        setMasterOtas(data);
      } else {
        console.error('Failed to fetch master OTAs');
      }
    } catch (error) {
      console.error('Error fetching master OTAs:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleOtaSelection = (otaId: string) => {
    setSelectedMasterOtaId(otaId);
    const selectedOta = masterOtas.find(ota => ota._id === otaId);
    if (selectedOta) {
      setFormData({
        ...formData,
        ota: selectedOta.otaName,
        masterOtaId: selectedOta._id,
        type: selectedOta.type,
        country: selectedOta.country,
        fop: selectedOta.fop,
        iata: selectedOta.iata,
        trueIata: selectedOta.trueIata,
        clientId: selectedOta.clientId,
        userId: selectedOta.userId,
        contact: selectedOta.contact
      });
    }
    setOtaSearchOpen(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      // Calculate weeksElapsed
      const currentDate = new Date();
      const startDate = new Date(formData.startDate);
      const timeDifference = currentDate.getTime() - startDate.getTime();
      const weeksElapsed = Math.round((timeDifference / (1000 * 60 * 60 * 24 * 7)) * 10) / 10;

      const response = await fetch('/api/quickwins', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          batch: formData.batch,
          ota: formData.ota,
          masterOtaId: selectedMasterOtaId,
          startDate: formData.startDate,
          weeks: parseInt(formData.weeks),
          weeksElapsed,
          status: formData.status,
          manager: formData.manager,
          comments: formData.comments,
        }),
      });

      if (response.ok) {
        setOpen(false);
        // Reset form data
        setFormData({
          batch: '25_02',
          ota: '',
          weeks: '2',
          manager: '',
          status: 'Initiation',
          startDate: new Date().toISOString().split("T")[0],
          lastCommDate: '',
          actualEndDate: '',
          comments: '',
        });
        setSelectedMasterOtaId('');
        
        // Refresh all integration-related data
        await Promise.all([
          mutate((key) => typeof key === 'string' && key.startsWith('/api/quickwins')),
          mutate('/api/quickwins/stats')
        ]);
      } else {
        console.error('Failed to create integration');
      }
    } catch (error) {
      console.error('Error creating integration:', error);
    }
  };

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerTrigger asChild>
        <Button variant="outline" size="sm" className="gap-2">
          <PlusCircle className="h-4 w-4" />
          New Quick Win
        </Button>
      </DrawerTrigger>
      <DrawerContent>
        <div className="mx-auto w-full max-w-4xl px-6">
          <DrawerHeader>
            <DrawerTitle className="text-2xl font-semibold">Add New Quick Win</DrawerTitle>
          </DrawerHeader>
          <form onSubmit={handleSubmit} className="py-4">
            {/* Basic Information */}
            <div className="mb-5">
              <h3 className="text-base font-semibold mb-2 border-b pb-1">Basic Information</h3>
              <div className="grid grid-cols-4 gap-x-4 gap-y-3">
                <div className="space-y-1">
                  <Label htmlFor="batch" className="text-sm">Batch ID</Label>
                  <Input
                    id="batch"
                    placeholder="e.g., 25_01"
                    value={formData.batch}
                    onChange={(e) => setFormData({ ...formData, batch: e.target.value })}
                    required
                    className="h-8"
                  />
                </div>
                <div className="space-y-1">
                  <Label htmlFor="otaSelect" className="text-sm">Select OTA</Label>
                  <Popover open={otaSearchOpen} onOpenChange={setOtaSearchOpen}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        role="combobox"
                        aria-expanded={otaSearchOpen}
                        className="h-8 w-full justify-between"
                      >
                        {selectedMasterOtaId
                          ? masterOtas.find((ota) => ota._id === selectedMasterOtaId)?.otaName
                          : "Select OTA"}
                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-full p-0">
                      <Command>
                        <CommandInput placeholder="Search OTA..." className="h-9" />
                        <CommandEmpty>No OTA found.</CommandEmpty>
                        <CommandList>
                          <CommandGroup>
                            {loading ? (
                              <CommandItem disabled>Loading...</CommandItem>
                            ) : masterOtas.length > 0 ? (
                              masterOtas.map((ota) => (
                                <CommandItem
                                  key={ota._id}
                                  value={ota.otaName}
                                  onSelect={() => handleOtaSelection(ota._id)}
                                >
                                  <Check
                                    className={cn(
                                      "mr-2 h-4 w-4",
                                      selectedMasterOtaId === ota._id ? "opacity-100" : "opacity-0"
                                    )}
                                  />
                                  {ota.otaName} {ota.country ? `(${ota.country})` : ''}
                                </CommandItem>
                              ))
                            ) : (
                              <CommandItem disabled>No OTAs found</CommandItem>
                            )}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                </div>
                <div className="space-y-1">
                  <Label htmlFor="type" className="text-sm">Integration Type</Label>
                  <Input
                    id="type"
                    value={formData.type || ''}
                    readOnly
                    disabled
                    className="h-8 bg-muted"
                  />
                </div>
                <div className="space-y-1">
                  <Label htmlFor="country" className="text-sm">Country</Label>
                  <Input
                    id="country"
                    value={formData.country || ''}
                    readOnly
                    disabled
                    className="h-8 bg-muted"
                  />
                </div>
                <div className="space-y-1">
                  <Label htmlFor="fop" className="text-sm">Form of Payment</Label>
                  <Input
                    id="fop"
                    value={formData.fop || ''}
                    readOnly
                    disabled
                    className="h-8 bg-muted"
                  />
                </div>
                <div className="space-y-1">
                  <Label htmlFor="manager" className="text-sm">Project Manager</Label>
                  <Input
                    id="manager"
                    placeholder="e.g., Sachin"
                    value={formData.manager}
                    onChange={(e) => setFormData({ ...formData, manager: e.target.value })}
                    required
                    className="h-8"
                  />
                </div>
              </div>
            </div>

            {/* Identification */}
            <div className="mb-5">
              <h3 className="text-base font-semibold mb-2 border-b pb-1">Identification & Access</h3>
              <div className="grid grid-cols-4 gap-x-4 gap-y-3">
                <div className="space-y-1">
                  <Label htmlFor="iata" className="text-sm">IATA Code</Label>
                  <Input
                    id="iata"
                    value={formData.iata || ''}
                    readOnly
                    disabled
                    className="h-8 bg-muted"
                  />
                </div>
                <div className="space-y-1">
                  <Label htmlFor="trueIata" className="text-sm">True IATA</Label>
                  <Input
                    id="trueIata"
                    value={formData.trueIata || ''}
                    readOnly
                    disabled
                    className="h-8 bg-muted"
                  />
                </div>
                <div className="space-y-1">
                  <Label htmlFor="clientId" className="text-sm">Client ID</Label>
                  <Input
                    id="clientId"
                    value={formData.clientId || ''}
                    readOnly
                    disabled
                    className="h-8 bg-muted"
                  />
                </div>
                <div className="space-y-1">
                  <Label htmlFor="userId" className="text-sm">User ID</Label>
                  <Input
                    id="userId"
                    value={formData.userId || ''}
                    readOnly
                    disabled
                    className="h-8 bg-muted"
                  />
                </div>
              </div>
            </div>

            {/* Timeline & Status */}
            <div className="mb-5">
              <h3 className="text-base font-semibold mb-2 border-b pb-1">Timeline & Status</h3>
              <div className="grid grid-cols-12 gap-x-3 gap-y-3">
                <div className="col-span-2 space-y-1">
                  <Label htmlFor="status" className="text-sm">Status</Label>
                  <Select
                    value={formData.status}
                    onValueChange={(value) => setFormData({ ...formData, status: value })}
                    required
                  >
                    <SelectTrigger className="h-8">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      {quickwinStatuses.map((status) => (
                        <SelectItem key={status} value={status}>
                          {status}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="col-span-1 space-y-1">
                  <Label htmlFor="weeks" className="text-sm">Weeks</Label>
                  <Input
                    id="weeks"
                    type="number"
                    min="1"
                    placeholder="e.g., 2"
                    value={formData.weeks}
                    onChange={(e) => setFormData({ ...formData, weeks: e.target.value })}
                    required
                    className="h-8"
                  />
                </div>
                <div className="col-span-3 space-y-1">
                  <Label htmlFor="startDate" className="text-sm">Start Date</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full h-8 justify-start text-left font-normal",
                          !formData.startDate && "text-muted-foreground"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-3 w-3" />
                        {formData.startDate ? format(new Date(formData.startDate), "PPP") : <span>Pick a date</span>}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={formData.startDate ? new Date(formData.startDate) : new Date()} // Default to current date
                        onSelect={(date) => {
                          if (date) {
                            const formattedDate = new Date(date.getTime() - date.getTimezoneOffset() * 60000)
                              .toISOString()
                              .split("T")[0]; // Adjust for timezone offset
                            setFormData({ ...formData, startDate: formattedDate });
                          }
                        }}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
                <div className="col-span-3 space-y-1">
                  <Label htmlFor="lastCommDate" className="text-sm">Last Communication</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full h-8 justify-start text-left font-normal",
                          !formData.lastCommDate && "text-muted-foreground"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-3 w-3" />
                        {formData.lastCommDate ? format(new Date(formData.lastCommDate), "PPP") : <span>Pick a date</span>}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={formData.lastCommDate ? new Date(formData.lastCommDate) : undefined}
                        onSelect={(date) => {
                          if (date) {
                            const formattedDate = new Date(date.getTime() - date.getTimezoneOffset() * 60000)
                              .toISOString()
                              .split("T")[0]; // Adjust for timezone offset
                            setFormData({ ...formData, lastCommDate: formattedDate });
                          }
                        }}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
                <div className="col-span-3 space-y-1">
                  <Label htmlFor="actualEndDate" className="text-sm">Actual End Date</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full h-8 justify-start text-left font-normal",
                          !formData.actualEndDate && "text-muted-foreground"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-3 w-3" />
                        {formData.actualEndDate ? format(new Date(formData.actualEndDate), "PPP") : <span>Pick a date</span>}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={formData.actualEndDate ? new Date(formData.actualEndDate) : undefined}
                        onSelect={(date) => {
                          if (date) {
                            const formattedDate = new Date(date.getTime() - date.getTimezoneOffset() * 60000)
                              .toISOString()
                              .split("T")[0]; // Adjust for timezone offset
                            setFormData({ ...formData, actualEndDate: formattedDate });
                          }
                        }}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
            </div>

            {/* Additional Information */}
            <div className="mb-4">
              <h3 className="text-base font-semibold mb-2 border-b pb-1">Additional Information</h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-1">
                  <Label htmlFor="comments" className="text-sm">Comments</Label>
                  <Input
                    id="comments"
                    placeholder="Add comments about this integration"
                    value={formData.comments}
                    onChange={(e) => setFormData({ ...formData, comments: e.target.value })}
                    className="h-8"
                  />
                </div>
                <div className="space-y-1">
                  <Label htmlFor="contacts" className="text-sm">Contacts</Label>
                  <Input
                    id="contacts"
                    value={formData.contact || ''}
                    readOnly
                    disabled
                    className="h-8 bg-muted"
                  />
                </div>
              </div>
            </div>

            <DrawerFooter className="px-0 pt-2">
              <Button type="submit">Create Quick Win</Button>
              <DrawerClose asChild>
                <Button variant="outline" type="button">Cancel</Button>
              </DrawerClose>
            </DrawerFooter>
          </form>
        </div>
      </DrawerContent>
    </Drawer>
  );
}