import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search } from "lucide-react";

interface SearchBarProps {
  searchInput: string;
  setSearchInput: (value: string) => void;
  handleSearch: () => void;
  handleKeyPress: (e: React.KeyboardEvent) => void;
  isSearching: boolean;
  searchTerm?: string;
  placeholder?: string;
}

/**
 * Search bar component for analytics
 */
export function SearchBar({
  searchInput,
  setSearchInput,
  handleSearch,
  handleKeyPress,
  isSearching,
  searchTerm,
  placeholder = "Search by OTA, Date"
}: SearchBarProps) {
  return (
    <div className="w-full">
      <div className="relative w-full">
        <Input
          placeholder={placeholder}
          className="w-full pr-100"
          value={searchInput}
          onChange={(e) => setSearchInput(e.target.value)}
          onKeyDown={handleKeyPress}
        />
        <Button
          size="icon"
          variant="ghost"
          className="absolute right-0 top-0 h-10 w-10"
          onClick={handleSearch}
          disabled={isSearching}
        >
          <Search/>
          <span className="sr-only">Search</span>
        </Button>
      </div>
      
      {searchTerm && (
        <div className="mt-2">
          <p className="text-sm" style={{ color: 'green' }}>
            Showing results for: <span>{searchTerm}</span>
          </p>
        </div>
      )}
    </div>
  );
}
