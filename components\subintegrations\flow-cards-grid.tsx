'use client';

import { SubIntegrationFlow } from '@/types';
import { StatusCard } from '@/components/subintegrations/status-card';

interface FlowCardsGridProps {
  displayFlowStats: {
    total: number;
    byFlow: Record<SubIntegrationFlow, number>;
  };
}

export function FlowCardsGrid({ displayFlowStats }: FlowCardsGridProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
      {displayFlowStats.byFlow &&
        Object.entries(displayFlowStats.byFlow).map(([flow, count]) => (
          <StatusCard
            key={flow}
            flow={flow as SubIntegrationFlow}
            count={count}
            total={displayFlowStats.total}
          />
        ))}
    </div>
  );
}
