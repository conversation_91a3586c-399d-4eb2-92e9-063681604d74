'use client';

import { useState, useMemo } from 'react';
import useSWR from 'swr';
import { Integration, IntegrationStatus, IntegrationStats } from '@/types';
import { fetcher } from '@/lib/api-utils';

/**
 * Custom hook for managing Integration stats
 * @returns Stats data and related state/functions
 */
export function useIntegrationStats() {
  const [filteredData, setFilteredData] = useState<Integration[]>([]);
  
  // Fetch stats from API
  const { data: stats, error: statsError } = useSWR<IntegrationStats>('/api/stats', fetcher);

  /**
   * Calculate stats based on filtered data
   */
  const calculateFilteredStats = (data: Integration[]): IntegrationStats => {
    if (!data || data.length === 0) {
      return {
        total: 0,
        byStatus: {
          Initiation: 0,
          Integration: 0,
          Testing: 0,
          Demo: 0,
          Onboarding: 0,
          Live: 0,
          Terminated: 0,
          Hold: 0,
        },
        averageCompletionTime: 0,
        delayedIntegrations: 0,
      };
    }

    const byStatus: Record<IntegrationStatus, number> = {
      Initiation: 0,
      Integration: 0,
      Testing: 0,
      Demo: 0,
      Onboarding: 0,
      Live: 0,
      Terminated: 0,
      Hold: 0,
    };

    let delayedIntegrations = 0;
    let completedCount = 0;
    let totalCompletionTime = 0;

    data.forEach(integration => {
      byStatus[integration.status as IntegrationStatus]++;

      if (integration.weeksElapsed > integration.weeks) {
        delayedIntegrations++;
      }

      if (integration.actualEndDate) {
        completedCount++;
        const startDate = new Date(integration.startDate);
        const endDate = new Date(integration.actualEndDate);
        totalCompletionTime += (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24 * 7);
      }
    });

    return {
      total: data.length,
      byStatus,
      averageCompletionTime: completedCount > 0 ? totalCompletionTime / completedCount : 0,
      delayedIntegrations,
    };
  };

  // Calculate display stats based on filtered data or original stats
  const displayStats = useMemo(() => {
    return filteredData.length > 0
      ? calculateFilteredStats(filteredData)
      : stats || calculateFilteredStats([]);
  }, [filteredData, stats]);

  return {
    stats,
    statsError,
    filteredData,
    setFilteredData,
    displayStats,
    isLoading: !stats && !statsError,
  };
}
