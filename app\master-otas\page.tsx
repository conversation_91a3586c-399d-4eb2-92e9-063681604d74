'use client';

import { useEffect, useState } from 'react';
import { MasterOTA } from '@/types';
import { Header } from "@/components/header";
import { MasterOtasList } from '@/components/master-otas/master-otas-list';
import { NewMasterOtaDialog } from '@/components/master-otas/new-master-ota-dialog';
import { OtaLinkingTool } from '@/components/master-otas/ota-linking-tool';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export default function MasterOTAsPage() {
  const [masterOtas, setMasterOtas] = useState<MasterOTA[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchMasterOtas = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/master-otas');
        const data = await response.json();
        setMasterOtas(data);
      } catch (error) {
        console.error('Error fetching master OTAs:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchMasterOtas();
  }, []);

  // Function to refresh data after changes
  const refreshData = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/master-otas');
      const data = await response.json();
      setMasterOtas(data);
    } catch (error) {
      console.error('Error refreshing master OTAs:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen">
      <Header />
      <div className="px-4 py-6">
        <div className="rounded-lg border shadow-sm p-6 mb-8">
          <Tabs defaultValue="list">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-semibold">Master OTA Management Hub</h2>
              <div className="flex items-center gap-4">
                <TabsList>
                  <TabsTrigger value="list">OTA List</TabsTrigger>
                  <TabsTrigger value="linking">OTA Linking</TabsTrigger>
                </TabsList>
                <NewMasterOtaDialog onSuccess={refreshData} />
              </div>
            </div>
            
            <TabsContent value="list">
              <MasterOtasList 
                masterOtas={masterOtas} 
                isLoading={isLoading} 
                onDataChange={refreshData} 
              />
            </TabsContent>
            
            <TabsContent value="linking">
              <div className="mb-4">
                <p className="text-muted-foreground">
                  Link master OTAs to existing integrations, quick wins, sub-integrations, and usages.
                </p>
              </div>
              <OtaLinkingTool masterOtas={masterOtas} onSuccess={refreshData} />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
} 