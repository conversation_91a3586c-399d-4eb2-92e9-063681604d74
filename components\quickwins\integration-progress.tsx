'use client';

import { QuickWin, QuickWinStats } from '@/types';
import { QuickWinsTable } from '@/components/quickwins/quickwins-table';
import { NewIntegrationDialog } from '@/components/quickwins/new-quickwin-dialog';

interface IntegrationProgressProps {
  displayStats: QuickWinStats;
  page: number;
  setPage: (page: number) => void;
  filteredData: QuickWin[];
  setFilteredData: (data: QuickWin[]) => void;
}

export function IntegrationProgress({
  displayStats,
  page,
  setPage,
  filteredData,
  setFilteredData
}: IntegrationProgressProps) {
  return (
    <div className="rounded-lg border shadow-sm p-6 mb-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <h2 className="text-2xl font-semibold">Integration Progress</h2>
        <div className="flex items-center gap-6">
          <div className="flex flex-col sm:flex-row gap-4 text-sm">
            <span className="flex items-center">
              <span className="w-3 h-3 rounded-full bg-blue-500 mr-2"></span>
              Average Completion: {displayStats.averageCompletionTime?.toFixed(1) || '0'} weeks
            </span>
            {/* <span className="flex items-center">
              <span className="w-3 h-3 rounded-full bg-red-500 mr-2"></span>
              Delayed Integrations: {displayStats.delayedIntegrations || 0}
            </span> */}
          </div>
          <NewIntegrationDialog />
        </div>
      </div>
      <QuickWinsTable
        currentPage={page}
        onPageChange={setPage}
        onFilteredDataChange={setFilteredData}
        quickwins={filteredData.length > 0 ? filteredData : []}
      />
    </div>
  );
}
