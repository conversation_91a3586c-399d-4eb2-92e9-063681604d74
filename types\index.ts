export type IntegrationType = 'B2B' | 'B2C' | 'PP' | 'Meta';

export const integrationStatuses = [
  'Initiation',
  'Integration',
  'Testing',
  'Demo',
  'Onboarding',
  'Live',
  'Terminated',
  'Hold',
] as const;

export const quickwinStatuses = [
  'Initiation',
  'Onboarding',
  'Live',
  'Hold',
] as const;

export const integrationTypes: IntegrationType[] = ['B2B', 'B2C', 'PP', 'Meta'];

export const formOfPaymentOptions = ['INVC', 'WBSP'] as const;

export type IntegrationStatus = typeof integrationStatuses[number];

export type QuickWinStatus = typeof quickwinStatuses[number];

export type FormOfPayment = typeof formOfPaymentOptions[number];

export interface Integration extends Document {
  _id: string;
  batch: string;
  masterOtaId: string | MasterOTA;
  startDate: Date;
  weeks: number;
  weeksElapsed: number;
  status: string;
  lastCommDate: Date;
  expectedEndDate: Date;
  manager: string;
  actualEndDate: Date;
  archivedDate: Date;
  archived: boolean;
  comments: string;
  ota: string;
  type: string;
  country: string;
  iata: string;
  trueIata: string;
  fop: string;
  clientId: string;
  userId: string;
  contact: string;
}

export interface IntegrationStats {
  total: number;
  byStatus: Record<IntegrationStatus, number>;
  averageCompletionTime: number;
  delayedIntegrations: number;
}

export interface QuickWinStats {
  total: number;
  byStatus: Record<QuickWinStatus, number>;
  averageCompletionTime: number;
  delayedIntegrations: number;
}

export interface QuickWin extends Document {
  _id: string;
  batch: string;
  masterOtaId: string | MasterOTA;
  startDate: Date;
  weeks: number;
  weeksElapsed: number;
  status: string;
  lastCommDate: Date;
  expectedEndDate: Date;
  manager: string;
  actualEndDate: Date;
  archivedDate: Date;
  archived: boolean;
  comments: string;
  ota: string;
  type: string;
  country: string;
  iata: string;
  trueIata: string;
  fop: string;
  clientId: string;
  userId: string;
  contact: string;
}

// Sub-Integration related types
export const subIntegrationFlows = [
  'Interline',
  'Ancillaries',
  'Seats',
  'Change',
  'Cancel',
  'Void',
  'Multi-City',
  'APIS'
] as const;

export const subIntegrationStatuses = [
  'Initiation',
  'Integration',
  'Testing',
  'Demo',
  'Live',
  'Hold',
] as const;

export type SubIntegrationFlow = typeof subIntegrationFlows[number];
export type SubIntegrationStatus = typeof subIntegrationStatuses[number];

export interface SubIntegration extends Document {
  _id: string;
  batch: string;
  masterOtaId: string | MasterOTA;
  flow: string;
  startDate: Date;
  weeks: number;
  progress: number;
  status: string;
  expectedEndDate: Date;
  manager: string;
  actualEndDate: Date;
  ota: string;
  country: string;
  iata: string;
  archived?: boolean;
  archivedDate?: Date;
}

export interface SubIntegrationStats {
  total: number;
  byStatus: Record<SubIntegrationStatus, number>;
  averageCompletionTime: number;
  delayedIntegrations: number;
}

interface IntegrationTypeStatus {
  live: boolean;
  liveDate: Date | null;
  lastUsedDate: Date | null;
}

export interface MasterOTA {
  _id: string;
  otaName: string;
  type: IntegrationType;
  country: string;
  iata: string;
  trueIata: string;
  fop: FormOfPayment;
  clientId: string;
  userId: string;
  techPartner?: string;
  contact: string;
  booking: IntegrationTypeStatus;
  ancillaries: IntegrationTypeStatus;
  seats: IntegrationTypeStatus;
  cancel: IntegrationTypeStatus;
  void: IntegrationTypeStatus;
  change: IntegrationTypeStatus;
  interline: IntegrationTypeStatus;
  multiCity: IntegrationTypeStatus;
  apis: IntegrationTypeStatus;
  createdAt?: Date;
  updatedAt?: Date;
  __v?: number;
}