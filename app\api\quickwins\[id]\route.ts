import { NextResponse } from 'next/server';
import { connectDB, QuickWin } from '../../../../lib/mongodb';
import mongoose from 'mongoose';

export const runtime = 'nodejs'; // Force Node.js runtime

export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB();
    
    const data = await request.json();
    const id = params.id;

    // Log the incoming data for debugging
    console.log('QuickWin update request received:');
    console.log('ID:', id);
    console.log('Data:', JSON.stringify(data, null, 2));
    console.log('archived:', data.archived);
    console.log('archivedDate:', data.archivedDate);

    // Validate ObjectId
    if (!id || !mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json({ error: 'Invalid QuickWin ID' }, { status: 400 });
    }

    // Fetch the existing quickwin to calculate weeksElapsed if not provided
    const existingQuickWin = await QuickWin.findById(id);
    if (!existingQuickWin) {
      return NextResponse.json({ error: 'QuickWin not found' }, { status: 404 });
    }

    const startDate = data.startDate ? new Date(data.startDate) : existingQuickWin.startDate;
    const actualEndDate = data.actualEndDate ? new Date(data.actualEndDate) : existingQuickWin.actualEndDate;
    const currentDate = new Date();

    // Calculate weeksElapsed dynamically if not provided
    const endDate = actualEndDate || currentDate;
    const timeDifference = endDate.getTime() - new Date(startDate).getTime();
    const weeksElapsed = data.weeksElapsed ?? Math.max(0, Math.round((timeDifference / (1000 * 60 * 60 * 24 * 7)) * 10) / 10);

    // Update the quickwin using updateOne to ensure all fields are properly updated
    const updateResult = await QuickWin.updateOne(
      { _id: id },
      {
        $set: {
          batch: data.batch,
          ota: data.ota,
          masterOtaId: data.masterOtaId,
          weeks: data.weeks,
          manager: data.manager,
          status: data.status,
          weeksElapsed,
          startDate: data.startDate ? new Date(data.startDate) : undefined,
          lastCommDate: data.lastCommDate ? new Date(data.lastCommDate) : undefined,
          actualEndDate: data.actualEndDate ? new Date(data.actualEndDate) : undefined,
          comments: data.comments,
          archived: data.archived !== undefined ? data.archived : existingQuickWin.archived,
          archivedDate: data.archivedDate ? new Date(data.archivedDate) : 
                        (data.archived && !existingQuickWin.archived) ? new Date() : 
                        existingQuickWin.archivedDate,
        }
      }
    );

    console.log('Update result:', updateResult);

    // If the archived update operation failed, try a more direct approach
    if (updateResult.acknowledged && updateResult.modifiedCount === 0) {
      console.log('First update attempt did not modify document. Trying direct archived flag update.');
      // Try a more direct approach specifically for archived flag
      if (data.archived === true) {
        const archiveResult = await QuickWin.updateOne(
          { _id: id },
          { 
            $set: { 
              archived: true,
              archivedDate: data.archivedDate ? new Date(data.archivedDate) : new Date()
            }
          }
        );
        console.log('Direct archive update result:', archiveResult);
      }
    }

    if (updateResult.acknowledged) {
      // Fetch the updated quickwin
      const updatedQuickWin = await QuickWin.findById(id);
      console.log('Updated QuickWin:', updatedQuickWin);
      return NextResponse.json({ success: true, data: updatedQuickWin });
    }

    return NextResponse.json({ error: 'QuickWin not found' }, { status: 404 });
  } catch (error) {
    console.error('Error updating QuickWin:', error);
    return NextResponse.json({ error: 'Failed to update QuickWin' }, { status: 500 });
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB();
    
    const id = params.id;

    // Validate ObjectId
    if (!id || !mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json({ error: 'Invalid QuickWin ID' }, { status: 400 });
    }

    const deletedQuickWin = await QuickWin.findByIdAndDelete(id);

    if (deletedQuickWin) {
      return NextResponse.json({ success: true });
    }

    return NextResponse.json({ error: 'QuickWin not found' }, { status: 404 });
  } catch (error) {
    console.error('Error deleting QuickWin:', error);
    return NextResponse.json({ error: 'Failed to delete QuickWin' }, { status: 500 });
  }
}