import { NextResponse } from 'next/server';
import * as XLSX from 'xlsx';
import { connectDB, LiveIntegration } from '@/lib/mongodb';
import { format } from 'date-fns';

export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

export async function GET(request: Request) {
  try {
    await connectDB();
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search') || '';

    // Build search query
    const query = search ? {
      $or: [
        { batch: { $regex: search, $options: 'i' } },
        { ota: { $regex: search, $options: 'i' } },
        { country: { $regex: search, $options: 'i' } },
        { status: { $regex: search, $options: 'i' } }
      ]
    } : {};

    // Fetch live integrations
    const liveIntegrations = await LiveIntegration.find(query).lean();

    // Transform data for Excel
    const data = liveIntegrations.map(integration => {
      const formatDate = (date: string | Date | null | undefined) => {
        if (!date) return 'N/A';
        try {
          return format(new Date(date), 'dd-MMM-yy');
        } catch {
          return 'N/A';
        }
      };

      return {
        'OTA': integration.ota,
        'Batch': integration.batch,
        'Type': integration.type,
        'Country': integration.country,
        'Form of Payment': integration.fop,
        'IATA': integration.iata,
        'True IATA': integration.trueIata,
        'Start Date': formatDate(integration.startDate),
        'Weeks': integration.weeks,
        'Weeks Elapsed': integration.weeksElapsed,
        'Status': integration.status,
        'Manager': integration.manager,
        'Archived Date': formatDate(integration.archivedDate),
        'Bookings': integration.metrics.bookings,
        'Revenue': integration.metrics.revenue,
        'Success Rate': `${integration.metrics.successRate}%`
      };
    });

    // Create workbook and worksheet
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.json_to_sheet(data);

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(wb, ws, 'Live Integrations');

    // Generate buffer
    const buf = XLSX.write(wb, { type: 'buffer', bookType: 'xlsx' });

    // Return the Excel file
    return new NextResponse(buf, {
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': 'attachment; filename=live-integrations.xlsx'
      }
    });

  } catch (error) {
    console.error('Error exporting live integrations:', error);
    return NextResponse.json({ error: 'Failed to export live integrations data' }, { status: 500 });
  }
} 