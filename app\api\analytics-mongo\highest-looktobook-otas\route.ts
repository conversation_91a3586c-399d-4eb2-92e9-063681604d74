import { NextResponse } from "next/server";
import { connectDB, Looks, Books } from "../../../../lib/mongodb";

export const runtime = "nodejs";
export const dynamic = 'force-dynamic';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const searchTerm = searchParams.get('search');
    
    // Connect to MongoDB
    await connectDB();
    
    // Aggregate looks data by OTA
    const looksByOta = await Looks.aggregate([
      {
        $group: {
          _id: "$otaName",
          Success: { $sum: "$success" },
          WrongRoutes: { $sum: "$wrongRoutes" },
          Throttled: { $sum: "$throttled" },
          Failures: { $sum: "$failures" }
        }
      }
    ]);
    
    // Format looks data
    const otaLooks: { [key: string]: { Success: number; WrongRoutes: number; Throttled: number; Failures: number } } = {};
    looksByOta.forEach((item) => {
      otaLooks[item._id] = {
        Success: item.Success || 0,
        WrongRoutes: item.WrongRoutes || 0,
        Throttled: item.Throttled || 0,
        Failures: item.Failures || 0
      };
    });

    // Aggregate booking data by OTA
    const bookingsByOta = await Books.aggregate([
      {
        $group: {
          _id: "$otaName",
          totalBookings: { $sum: "$bookingCount" }
        }
      }
    ]);
    
    // Format bookings data
    const otaBookings: { [key: string]: number } = {};
    bookingsByOta.forEach((item) => {
      otaBookings[item._id] = item.totalBookings;
    });
    
    // Combine all OTAs from both datasets
    const allOtas = [
      ...Object.keys(otaBookings),
      ...Object.keys(otaLooks)
    ].filter((value, index, self) => self.indexOf(value) === index);
    
    // Exclude meta search engines
    const metaSearchEngines = ["Google", "TripAdvisor", "KAYAK", "Skyscanner"];
    const filteredOtas = allOtas.filter(ota => !metaSearchEngines.includes(ota));
      // Calculate and format the response data
    const otaData = filteredOtas.map((otaName) => {
      const lookData = otaLooks[otaName] || { Success: 0, WrongRoutes: 0, Throttled: 0, Failures: 0 };
      const bookings = otaBookings[otaName] || 0;
      
      // Calculate totals and ratios
      const successLooks = lookData.Success || 0;
      const totalLooks = successLooks + lookData.WrongRoutes + lookData.Throttled + lookData.Failures;
      
      // Calculate look-to-book ratio (how many successful looks per booking)
      // Only calculate if both success looks and bookings are present
      const lookToBook = (successLooks > 0 && bookings > 0) 
        ? Math.round(successLooks / bookings) 
        : 0;
      
      // Format the OTA name - capitalize first letter if needed
      const formattedOtaName = otaName.includes('_') ? otaName : otaName.charAt(0).toUpperCase() + otaName.slice(1);
      
      return {
        OTAName: formattedOtaName,
        TotalLooks: totalLooks,
        SuccessLooks: successLooks,
        TotalBookings: bookings,
        LookToBookRatio: lookToBook,
      };
    });
      // Filter for OTAs with significant traffic and at least 1 booking
    // We need at least 50 successful looks and at least 1 booking to calculate a meaningful ratio
    const significantOtas = otaData.filter(ota => 
      ota.SuccessLooks >= 50 && 
      ota.TotalBookings > 0 && 
      ota.LookToBookRatio > 0
    );
    
    // Apply search term filter if provided
    const filteredResults = searchTerm 
      ? significantOtas.filter(ota => ota.OTAName.toLowerCase().includes(searchTerm.toLowerCase()))
      : significantOtas;
    
    // Sort by look-to-book ratio (highest first) and take top 25
    const highestLookToBookOtas = filteredResults
      .sort((a, b) => b.LookToBookRatio - a.LookToBookRatio)
      .slice(0, 25);
    
    return NextResponse.json({
      success: true,
      data: highestLookToBookOtas
    });
    
  } catch (error) {
    console.error("Error fetching highest look-to-book OTAs data:", error);
    return NextResponse.json({ success: false, error: (error as Error).message }, { status: 500 });
  }
}
