'use client';

import { Integration, IntegrationStats } from '@/types';
import { IntegrationTable } from '@/components/dashboard/integration-table';
import { NewIntegrationDialog } from '@/components/dashboard/new-integration-dialog';

interface IntegrationProgressSectionProps {
  displayStats: IntegrationStats;
  page: number;
  setPage: (page: number) => void;
  setFilteredData: (data: Integration[]) => void;
}

export function IntegrationProgressSection({
  displayStats,
  page,
  setPage,
  setFilteredData
}: IntegrationProgressSectionProps) {
  return (
    <div className="rounded-lg border shadow-sm p-6 mb-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <h2 className="text-2xl font-semibold">Integration Progress</h2>
        <div className="flex items-center gap-6">
          <div className="flex flex-col sm:flex-row gap-4 text-sm">
            <span className="flex items-center">
              <span className="w-3 h-3 rounded-full bg-primary mr-2"></span>
              Average Completion: {displayStats.averageCompletionTime?.toFixed(1) || '0'} weeks
            </span>
            {/* <span className="flex items-center">
              <span className="w-3 h-3 rounded-full bg-secondary mr-2"></span>
              Delayed Integrations: {displayStats.delayedIntegrations || 0}
            </span> */}
          </div>
          <NewIntegrationDialog />
        </div>
      </div>
      <IntegrationTable
        currentPage={page}
        onPageChange={setPage}
        onFilteredDataChange={setFilteredData}
      />
    </div>
  );
}
