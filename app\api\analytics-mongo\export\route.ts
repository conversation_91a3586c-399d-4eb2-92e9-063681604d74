import { NextResponse } from 'next/server';
import * as XLSX from 'xlsx';
import { connectDB, Looks, Books } from "../../../../lib/mongodb";

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';
export const maxDuration = 120; // Set maximum duration to 120 seconds for larger exports

// Helper function to format date for display
const formatDate = (date: Date): string => {
  const day = date.getDate().toString().padStart(2, '0');
  const month = date.toLocaleString('en-US', { month: 'short' });
  const year = date.getFullYear().toString().substring(2);
  return `${day}-${month}-${year}`;
};

// Helper function to get date range for a specific month and year
const getMonthDateRange = (month: number, year: number): { startDate: Date, endDate: Date } => {
  // Month is 0-indexed in JavaScript Date (0 = January, 11 = December)
  const startDate = new Date(year, month, 1);
  // Set to first day of next month, then subtract 1 millisecond to get last day of current month
  const endDate = new Date(year, month + 1, 1);
  endDate.setMilliseconds(-1);

  return { startDate, endDate };
};

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const searchTerm = searchParams.get('search') || '';
    const month = searchParams.get('month'); // Format: MM (01-12)
    const year = searchParams.get('year');   // Format: YYYY
    const otaName = searchParams.get('ota'); // OTA name to filter by

    // Connect to MongoDB
    await connectDB();

    // Create query for both collections
    let query: any = {};

    // Filter by month and year if provided
    if (month && year) {
      const monthNum = parseInt(month) - 1; // Convert to 0-indexed month
      const yearNum = parseInt(year);

      if (!isNaN(monthNum) && !isNaN(yearNum) && monthNum >= 0 && monthNum <= 11) {
        const { startDate, endDate } = getMonthDateRange(monthNum, yearNum);
        query.date = { $gte: startDate, $lte: endDate };
      }
    }

    // Filter by OTA name if provided
    if (otaName) {
      query.otaName = { $regex: otaName, $options: 'i' };
    }

    // Handle search term if month/year and ota are not provided
    if (!month && !year && !otaName && searchTerm) {
      // Check if the search term is a date or an OTA name
      const isDate = searchTerm.match(/^\d{1,2}[-\/]\w+[-\/]\d{2,4}$/);

      if (isDate) {
        // Try to parse the date
        try {
          const searchDate = new Date(searchTerm);
          if (!isNaN(searchDate.getTime())) {
            // If valid date, search for records on that date
            const startDate = new Date(searchDate.setHours(0, 0, 0, 0));
            const endDate = new Date(searchDate.setHours(23, 59, 59, 999));
            query.date = { $gte: startDate, $lte: endDate };
          }
        } catch (e) {
          // If date parsing fails, use text search
          query.otaName = { $regex: searchTerm, $options: 'i' };
        }
      } else {
        // Assume it's an OTA name search
        query.otaName = { $regex: searchTerm, $options: 'i' };
      }
    }

    // Create a more efficient aggregation pipeline with proper typing
    const pipeline = [
      { $match: query },
      { $sort: { date: 1, otaName: 1 } as const },
      // Perform a more efficient lookup by using the indexed fields
      {
        $lookup: {
          from: "books",
          let: { lookDate: "$date", otaName: "$otaName" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$otaName", "$$otaName"] },
                    { $eq: ["$date", "$$lookDate"] }
                  ]
                }
              }
            },
            // Project only the fields we need from books
            {
              $project: {
                bookingCount: 1,
                _id: 0
              }
            }
          ],
          as: "bookings"
        }
      },
      // Optimize the projection to calculate only what we need
      {
        $project: {
          _id: 0,
          Date: "$date",
          OTAName: "$otaName",
          Success: { $ifNull: ["$success", 0] },
          WrongRoutes: { $ifNull: ["$wrongRoutes", 0] },
          Throttled: { $ifNull: ["$throttled", 0] },
          Failures: { $ifNull: ["$failures", 0] },
          Bookings: { $sum: "$bookings.bookingCount" },
          TotalLooks: { $add: ["$success", "$wrongRoutes", "$throttled", "$failures"] }
        }
      },
      // Calculate LookToBook ratio
      {
        $addFields: {
          LookToBook: {
            $cond: [
              { $gt: ["$Bookings", 0] },
              { $trunc: [{ $divide: ["$TotalLooks", "$Bookings"] }] },
              0
            ]
          }
        }
      }
    ];

    // Execute the aggregation pipeline
    const cursor = Looks.aggregate(pipeline);

    // Get count of records for progress tracking
    const recordCount = await Looks.countDocuments(query);
    console.log(`Exporting ${recordCount} records`);

    // Create workbook and worksheet
    const wb = XLSX.utils.book_new();

    // Create a descriptive sheet name based on the filter
    let sheetName = 'Analytics Data';
    let fileName = 'analytics-mongodb';

    if (month && year) {
      const monthNum = parseInt(month) - 1;
      const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
                         'July', 'August', 'September', 'October', 'November', 'December'];
      sheetName = `${monthNames[monthNum]} ${year}`;
      fileName = `analytics-${monthNames[monthNum]}-${year}`;
    } else if (otaName) {
      sheetName = otaName;
      fileName = `analytics-${otaName}`;
    }

    // Initialize worksheet with headers
    const ws = XLSX.utils.aoa_to_sheet([
      ['Date', 'OTA Name', 'Success', 'Wrong Routes', 'Throttled', 'Failures', 'Total Looks', 'Bookings', 'Look To Book']
    ]);

    // Process data in batches to reduce memory usage
    const batchSize = 1000;
    let rowIndex = 1;
    let batch = [];
    let processedCount = 0;

    // Process the cursor in batches
    for await (const doc of cursor) {
      // Format the date
      const formattedDate = formatDate(doc.Date);

      // Add row to the batch
      batch.push([
        formattedDate,
        doc.OTAName,
        doc.Success,
        doc.WrongRoutes,
        doc.Throttled,
        doc.Failures,
        doc.TotalLooks,
        doc.Bookings,
        doc.LookToBook
      ]);

      processedCount++;

      // If batch is full, add to worksheet
      if (batch.length >= batchSize) {
        XLSX.utils.sheet_add_aoa(ws, batch, { origin: rowIndex });
        rowIndex += batch.length;
        batch = [];

        // Log progress
        console.log(`Processed ${processedCount} of ${recordCount} records (${Math.round(processedCount/recordCount*100)}%)`);
      }
    }

    // Add any remaining items
    if (batch.length > 0) {
      XLSX.utils.sheet_add_aoa(ws, batch, { origin: rowIndex });
    }

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(wb, ws, sheetName);

    // Generate buffer
    const buf = XLSX.write(wb, { type: 'buffer', bookType: 'xlsx' });

    // Return the Excel file
    return new NextResponse(buf, {
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': `attachment; filename=${fileName}.xlsx`
      }
    });
  } catch (error) {
    console.error('Error exporting analytics from MongoDB:', error);
    return NextResponse.json({ error: 'Failed to export analytics data' }, { status: 500 });
  }
}