import { NextResponse } from "next/server";
import { connectDB, Looks, Books } from "../../../../lib/mongodb";

export const runtime = "nodejs";
export const dynamic = 'force-dynamic';

export async function GET() {
  try {
    // Connect to MongoDB
    await connectDB();
    
    // Aggregate looks data by OTA
    const looksByOta = await Looks.aggregate([
      {
        $group: {
          _id: "$otaName",
          Success: { $sum: "$success" },
          WrongRoutes: { $sum: "$wrongRoutes" },
          Throttled: { $sum: "$throttled" },
          Failures: { $sum: "$failures" }
        }
      }
    ]);
    
    // Format looks data
    const otaLooks: { [key: string]: { Success: number; WrongRoutes: number; Throttled: number; Failures: number } } = {};
    looksByOta.forEach((item) => {
      otaLooks[item._id] = {
        Success: item.Success || 0,
        WrongRoutes: item.WrongRoutes || 0,
        Throttled: item.Throttled || 0,
        Failures: item.Failures || 0
      };
    });

    // Aggregate booking data by OTA
    const bookingsByOta = await Books.aggregate([
      {
        $group: {
          _id: "$otaName",
          totalBookings: { $sum: "$bookingCount" }
        }
      }
    ]);
    
    // Format bookings data
    const otaBookings: { [key: string]: number } = {};
    bookingsByOta.forEach((item) => {
      otaBookings[item._id] = item.totalBookings;
    });
    
    // Calculate total bookings across all OTAs
    const totalBookings = Object.values(otaBookings).reduce((sum, count) => sum + (count || 0), 0);
    
    // Combine all OTAs from both datasets
    const allOtas = [
      ...Object.keys(otaBookings),
      ...Object.keys(otaLooks)
    ].filter((value, index, self) => self.indexOf(value) === index);
    
    // Calculate and format the response data
    const otaData = allOtas.map((otaName) => {
      const lookData = otaLooks[otaName] || { Success: 0, WrongRoutes: 0, Throttled: 0, Failures: 0 };
      const bookings = otaBookings[otaName] || 0;
      
      // Calculate totals and rates
      const totalLooks = lookData.Success + lookData.WrongRoutes + lookData.Throttled + lookData.Failures;
      const lookToBook = bookings > 0 ? Math.round(lookData.Success / bookings) : 0; // Only use Success for LookToBook
      const bookingPercentage = totalBookings > 0 ? (bookings / totalBookings) * 100 : 0;
      
      // Calculate rates
      const successRate = totalLooks > 0 ? (lookData.Success / totalLooks) * 100 : 0;
      const wrongRoutesRate = totalLooks > 0 ? (lookData.WrongRoutes / totalLooks) * 100 : 0;
      const throttledRate = totalLooks > 0 ? (lookData.Throttled / totalLooks) * 100 : 0;
      const failureRate = totalLooks > 0 ? (lookData.Failures / totalLooks) * 100 : 0;
      
      // Format the OTA name - capitalize first letter if needed
      const formattedOtaName = otaName.includes('_') ? otaName : otaName.charAt(0).toUpperCase() + otaName.slice(1);
      
      return {
        OTAName: formattedOtaName,
        TotalLooks: totalLooks,
        TotalBookings: bookings,
        LookToBook: lookToBook,
        BookingPercentage: bookingPercentage.toFixed(2),
        SuccessRate: parseFloat(successRate.toFixed(1)),
        WrongRoutesRate: parseFloat(wrongRoutesRate.toFixed(1)),
        ThrottledRate: parseFloat(throttledRate.toFixed(1)),
        FailureRate: parseFloat(failureRate.toFixed(1)),
        // For ranking purposes: use the combined failure, throttle and wrong route rates
        TotalFailureRate: parseFloat((failureRate + throttledRate + wrongRoutesRate).toFixed(1))
      };
    });
    
    // Only consider OTAs with significant traffic (at least 100 total looks)
    const significantOtas = otaData.filter(ota => ota.TotalLooks >= 100);
    
    // Sort by total failure rate (highest first) and take top 25
    const worstOtas = significantOtas
      .sort((a, b) => b.TotalFailureRate - a.TotalFailureRate)
      .slice(0, 25);
    
    return NextResponse.json({
      success: true,
      data: worstOtas
    });
  } catch (error) {
    console.error("Error fetching worst OTAs data from MongoDB:", error);
    return NextResponse.json({ success: false, error: (error as Error).message }, { status: 500 });
  }
}
