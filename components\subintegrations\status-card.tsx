"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { SubIntegrationFlow } from "@/types";
import { getFlowColor } from "@/lib/format-utils";

interface FlowCardProps {
  flow: SubIntegrationFlow;
  count: number;
  total: number;
};

export function StatusCard({ flow, count, total }: FlowCardProps) {
  const percentage = total > 0 ? ((count / total) * 100).toFixed(1) : "0";
  const flowColor = getFlowColor(flow);

  return (
    <Card className="shadow-sm hover:shadow-md transition-shadow">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className={`text-lg font-bold ${flowColor}`}>{flow}</CardTitle>
        <div className={`text-2xl font-bold ${flowColor}`}>{count}</div>
      </CardHeader>
      <CardContent>
        <div className="text-xs text-muted-foreground">
          {percentage}% of total integrations
        </div>
      </CardContent>
    </Card>
  );
} 