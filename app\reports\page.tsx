'use client';

import { Head<PERSON> } from "@/components/header";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>G<PERSON>, LabelList, <PERSON>Axis, <PERSON>Axis } from "recharts";
import LiveIntegrationsTable from '@/components/live-integrations/liveintegrations-table';
import useSWR from 'swr';
import React from "react";

const fetcher = (url: string) => fetch(url).then(res => res.json());

const calculateFilteredStats = (data: any[]) => {
  if (!data || data.length === 0) {
    return {
      total: 0,
      byStatus: {},
      averageCompletionTime: 0,
      delayedIntegrations: 0,
    };
  }

  const byStatus: Record<string, number> = {};
  let delayedIntegrations = 0;
  let completedCount = 0;
  let totalCompletionTime = 0;

  data.forEach(integration => {
    byStatus[integration.status] = (byStatus[integration.status] || 0) + 1;

    if (integration.weeksElapsed > integration.weeks) {
      delayedIntegrations++;
    }

    if (integration.actualEndDate) {
      completedCount++;
      const startDate = new Date(integration.startDate);
      const endDate = new Date(integration.actualEndDate);
      totalCompletionTime += (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24 * 7);
    }
  });

  return {
    total: data.length,
    byStatus,
    averageCompletionTime: completedCount > 0 ? totalCompletionTime / completedCount : 0,
    delayedIntegrations,
  };
};

const getStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    Initiation: "#00355F",
    Integration: "#F59E0B", // yellow-500
    Testing: "#A855F7", // purple-500
    Demo: "#6366F1", // indigo-500
    Onboarding: "#3B82F6", // blue-500
    Live: "#10B981", // green-500
    Terminated: "#EF4444", // red-500
    Hold: "#6B7280", // gray-500
  };
  return colors[status] || "#6B7280"; // Default to gray-500
};

export default function ReportsPage() {
  const { data: stats, error } = useSWR('/api/stats', fetcher);
  const { data: integrations } = useSWR('/api/integrations?page=1&pageSize=150', fetcher);
  
  // For LiveIntegrationsTable - fetch only Live status
  const { data: liveIntegrationsData } = useSWR('/api/live-integrations?status=Live&page=1&pageSize=25', fetcher);

  if (error) return <div>Failed to load reports</div>;
  if (!stats || !integrations) return <div>Loading...</div>;

  const displayStats = calculateFilteredStats(integrations.data);

  const statusData = Object.entries(displayStats.byStatus).map(([status, count]) => ({
    name: status,
    value: count,
    fill: getStatusColor(status),
  }));

  const typeDistribution = integrations.data.reduce((acc: Record<string, number>, curr: { type: string }) => {
    acc[curr.type] = (acc[curr.type] || 0) + 1;
    return acc;
  }, {});

  const countryDistribution = integrations.data.reduce((acc: Record<string, number>, curr: { country: string }) => {
    acc[curr.country] = (acc[curr.country] || 0) + 1;
    return acc;
  }, {});

  const countryData = Object.entries(countryDistribution)
    .sort((a, b) => (b[1] as number) - (a[1] as number))
    .slice(0, 10)
    .map(([country, count]) => ({
      name: country,
      count: count,
      fill: "#3B82F6", // Default blue color for country bars
    }));

  return (
    <div className="min-h-screen">
      <Header />
      <div className="mt-8">
          <Card>
            <CardHeader>
              <CardTitle>Live Integrations</CardTitle>
            </CardHeader>
            <CardContent>
              <LiveIntegrationsTable initialData={liveIntegrationsData} />
            </CardContent>
          </Card>
        </div>
      <div className="px-4 py-6">
        <div className="space-y-8">
          <Card>
            <CardHeader>
              <CardTitle>Overview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-lg font-semibold">Average Integration Completion Time</p>
                  <p className="text-3xl font-bold">
                    {displayStats.averageCompletionTime ? displayStats.averageCompletionTime.toFixed(1) : 'N/A'} weeks
                  </p>
                </div>
                <div>
                  <p className="text-lg font-semibold">Delayed Integrations</p>
                  <p className="text-3xl font-bold text-red-600">
                    {displayStats.delayedIntegrations}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Integration Status</CardTitle>
              </CardHeader>
              <CardContent>
                <ChartContainer config={{ status: { label: "Status" } }}>
                  <BarChart
                    accessibilityLayer
                    data={statusData}
                    layout="vertical"
                    margin={{ left: 0 }}
                    width={400}
                    height={300}
                  >
                    <YAxis
                      dataKey="name"
                      type="category"
                      tickLine={false}
                      tickMargin={10}
                      axisLine={false}
                      width={90}
                      tickFormatter={(value) =>
                        value
                      }
                    />
                    <XAxis dataKey="value" type="number" hide />
                    <ChartTooltip
                      cursor={false}
                      content={<ChartTooltipContent hideLabel />}
                    />
                    <Bar
                      dataKey="value"
                      layout="vertical"
                      radius={5}
                    >
                      <LabelList
                        dataKey="value"
                        position="insideRight"
                        className="fill-white"
                        fontSize={12}
                      />
                    </Bar>
                  </BarChart>
                </ChartContainer>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Integration Status by Countries</CardTitle>
              </CardHeader>
              <CardContent>
                <ChartContainer config={{ country: { label: "Country" } }}>
                  <BarChart
                    accessibilityLayer
                    data={countryData}
                    layout="vertical"
                    margin={{ left: 0 }}
                    width={400}
                    height={300}
                  >
                    <YAxis
                      dataKey="name"
                      type="category"
                      tickLine={false}
                      tickMargin={10}
                      axisLine={false}
                      width={90}
                      tickFormatter={(value) => value}
                    />
                    <XAxis dataKey="count" type="number" hide />
                    <ChartTooltip
                      cursor={false}
                      content={<ChartTooltipContent hideLabel />}
                    />
                    <Bar dataKey="count" layout="vertical" radius={5}>
                      <LabelList
                        dataKey="count"
                        position="insideRight"
                        className="fill-white"
                        fontSize={12}
                      />
                    </Bar>
                  </BarChart>
                </ChartContainer>
              </CardContent>
            </Card>
          </div>
        </div>
        

      </div>
    </div>
  );
}