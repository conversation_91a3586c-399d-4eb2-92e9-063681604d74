'use client';

import { QuickWinStatus, QuickWinStats } from '@/types';
import { StatusCard } from '@/components/dashboard/status-card';

interface StatsCardsGridProps {
  displayStats: QuickWinStats;
}

export function StatsCardsGrid({ displayStats }: StatsCardsGridProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
      {displayStats.byStatus &&
        Object.entries(displayStats.byStatus).map(([status, count]) => (
          <StatusCard
            key={status}
            status={status as QuickWinStatus}
            count={count}
            total={displayStats.total}
          />
        ))}
    </div>
  );
}
