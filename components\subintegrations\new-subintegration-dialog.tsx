"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Drawer,
  DrawerClose,
  Drawer<PERSON>onte<PERSON>,
  <PERSON>er<PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DrawerFooter,
} from "@/components/ui/drawer";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { PlusCircle, Calendar as CalendarIcon, Check, ChevronsUpDown } from "lucide-react";
import { useState, useEffect } from "react";
import { SubIntegrationFlow, SubIntegrationStatus, subIntegrationFlows, subIntegrationStatuses } from "@/types";
import { mutate } from "swr";
import { format } from "date-fns";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";

interface MasterOTA {
  _id: string;
  otaName: string;
  type: string;
  country: string;
  iata: string;
  trueIata: string;
  fop: string;
  clientId: string;
  userId: string;
  contact: string;
}

export function NewSubIntegrationDialog() {
  const [open, setOpen] = useState(false);
  const [masterOtas, setMasterOtas] = useState<MasterOTA[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedMasterOtaId, setSelectedMasterOtaId] = useState<string>('');
  const [otaSearchOpen, setOtaSearchOpen] = useState(false);
  const [formData, setFormData] = useState({
    batch: '25_02',
    ota: '',
    country: '',
    flow: 'Ancillaries' as SubIntegrationFlow,
    iata: '',
    startDate: new Date().toISOString().split("T")[0], // Default to current date
    weeks: '6',
    status: 'Initiation' as SubIntegrationStatus,
    manager: '',
    actualEndDate: '',
  });

  // Fetch master OTAs when the dialog opens
  useEffect(() => {
    if (open) {
      fetchMasterOtas();
    }
  }, [open]);

  const fetchMasterOtas = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/master-otas');
      if (response.ok) {
        const data = await response.json();
        if (Array.isArray(data) && data.length > 0) {
          setMasterOtas(data);
        } else {
          console.error('Received empty or invalid Master OTAs array');
        }
      } else {
        console.error('Failed to fetch master OTAs');
      }
    } catch (error) {
      console.error('Error fetching master OTAs:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleOtaSelection = (otaId: string) => {
    setSelectedMasterOtaId(otaId);
    const selectedOta = masterOtas.find(ota => ota._id === otaId);
    if (selectedOta) {
      setFormData({
        ...formData,
        ota: selectedOta.otaName,
        country: selectedOta.country || formData.country,
        iata: selectedOta.iata || formData.iata,
      });
    }
    setOtaSearchOpen(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const response = await fetch('/api/subintegrations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          weeks: parseInt(formData.weeks),
          masterOtaId: selectedMasterOtaId || undefined, // Include masterOtaId if selected
        }),
      });

      if (response.ok) {
        setOpen(false);
        // Reset form data
        setFormData({
          batch: '25_02',
          ota: '',
          country: '',
          flow: 'Ancillaries',
          iata: '',
          startDate: new Date().toISOString().split("T")[0], // Default to current date
          weeks: '6',
          status: 'Initiation',
          manager: '',
          actualEndDate: '',
        });
        setSelectedMasterOtaId('');
        
        // Refresh all subintegration-related data
        await Promise.all([
          mutate((key) => typeof key === 'string' && key.startsWith('/api/subintegrations')),
          mutate('/api/subintegrations/stats')
        ]);
      } else {
        console.error('Failed to create subintegration');
      }
    } catch (error) {
      console.error('Error creating subintegration:', error);
    }
  };

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerTrigger asChild>
        <Button variant="outline" size="sm" className="gap-2">
          <PlusCircle className="h-4 w-4" />
          New Sub-Integration
        </Button>
      </DrawerTrigger>
      <DrawerContent>
        <div className="mx-auto w-full max-w-4xl px-6">
          <DrawerHeader>
            <DrawerTitle className="text-2xl font-semibold">Add New OTA Sub-Integration</DrawerTitle>
          </DrawerHeader>
          <form onSubmit={handleSubmit} className="py-4">
            {/* Basic Information */}
            <div className="mb-3 max-w-3xl mx-auto">
              <h3 className="text-base font-semibold mb-1 border-b pb-1">Basic Information</h3>
              <div className="grid grid-cols-4 gap-x-3 gap-y-2">
                <div className="space-y-1">
                  <Label htmlFor="batch" className="text-sm">Batch ID</Label>
                  <Input
                    id="batch"
                    placeholder="e.g., 25_01"
                    value={formData.batch}
                    onChange={(e) => setFormData({ ...formData, batch: e.target.value })}
                    required
                    className="h-8"
                  />
                </div>
                <div className="space-y-1">
                  <Label htmlFor="otaSelect" className="text-sm font-bold">Select Master OTA</Label>
                  <Popover open={otaSearchOpen} onOpenChange={setOtaSearchOpen}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        role="combobox"
                        aria-expanded={otaSearchOpen}
                        className="h-8 w-full justify-between bg-muted-foreground/5 border-purple-300"
                      >
                        {selectedMasterOtaId
                          ? masterOtas.find((ota) => ota._id === selectedMasterOtaId)?.otaName
                          : "Select Master OTA"}
                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-full p-0">
                      <Command>
                        <CommandInput placeholder="Search Master OTA..." className="h-9" />
                        <CommandEmpty>No OTA found.</CommandEmpty>
                        <CommandList>
                          <CommandGroup>
                            {loading ? (
                              <CommandItem disabled>Loading Master OTAs...</CommandItem>
                            ) : masterOtas.length > 0 ? (
                              masterOtas.map((ota) => (
                                <CommandItem
                                  key={ota._id}
                                  value={ota.otaName}
                                  onSelect={() => handleOtaSelection(ota._id)}
                                >
                                  <Check
                                    className={cn(
                                      "mr-2 h-4 w-4",
                                      selectedMasterOtaId === ota._id ? "opacity-100" : "opacity-0"
                                    )}
                                  />
                                  {ota.otaName} {ota.country ? `(${ota.country})` : ''}
                                </CommandItem>
                              ))
                            ) : (
                              <CommandItem disabled>No OTAs found</CommandItem>
                            )}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                </div>
                <div className="space-y-1">
                  <Label htmlFor="country" className="text-sm">Country</Label>
                  <Input
                    id="country"
                    placeholder="e.g., UAE"
                    value={formData.country}
                    onChange={(e) => setFormData({ ...formData, country: e.target.value })}
                    required
                    className="h-8"
                    disabled={!!selectedMasterOtaId}
                  />
                </div>
                <div className="space-y-1">
                  <Label htmlFor="flow" className="text-sm">Flow Type</Label>
                  <Select
                    value={formData.flow}
                    onValueChange={(value) => setFormData({ ...formData, flow: value as SubIntegrationFlow })}
                    required
                  >
                    <SelectTrigger className="h-8">
                      <SelectValue placeholder="Flow" />
                    </SelectTrigger>
                    <SelectContent>
                      {subIntegrationFlows.map((flow) => (
                        <SelectItem key={flow} value={flow}>
                          {flow}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-1">
                  <Label htmlFor="iata" className="text-sm">IATA</Label>
                  <Input
                    id="iata"
                    placeholder="e.g., 1003081P"
                    value={formData.iata}
                    onChange={(e) => setFormData({ ...formData, iata: e.target.value })}
                    required
                    className="h-8"
                    disabled={!!selectedMasterOtaId}
                  />
                </div>
                <div className="space-y-1">
                  <Label htmlFor="manager" className="text-sm">Manager</Label>
                  <Input
                    id="manager"
                    placeholder="e.g., Sachin"
                    value={formData.manager}
                    onChange={(e) => setFormData({ ...formData, manager: e.target.value })}
                    required
                    className="h-8"
                  />
                </div>
              </div>
            </div>

            {/* Timeline and Status */}
            <div className="mb-3 max-w-3xl mx-auto">
              <h3 className="text-base font-semibold mb-1 border-b pb-1">Timeline and Status</h3>
              <div className="grid grid-cols-4 gap-x-3 gap-y-2">
                <div className="space-y-1">
                  <Label htmlFor="status" className="text-sm">Status</Label>
                  <Select
                    value={formData.status}
                    onValueChange={(value) => setFormData({ ...formData, status: value as SubIntegrationStatus })}
                    required
                  >
                    <SelectTrigger className="h-8">
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      {subIntegrationStatuses.map((status) => (
                        <SelectItem key={status} value={status}>
                          {status}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-1">
                  <Label htmlFor="startDate" className="text-sm">Start Date</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal h-8",
                          !formData.startDate && "text-muted-foreground"
                        )}
                      >
                        <CalendarIcon className="mr-1 h-3 w-3" />
                        {formData.startDate ? format(new Date(formData.startDate), "dd MMM yy") : <span>Pick date</span>}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={new Date(formData.startDate)}
                        onSelect={(date) => date && setFormData({ ...formData, startDate: date.toISOString().split("T")[0] })}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
                <div className="space-y-1">
                  <Label htmlFor="weeks" className="text-sm">Weeks</Label>
                  <Input
                    id="weeks"
                    type="number"
                    min="1"
                    max="52"
                    value={formData.weeks}
                    onChange={(e) => setFormData({ ...formData, weeks: e.target.value })}
                    required
                    className="h-8"
                  />
                </div>
                <div className="space-y-1">
                  <Label htmlFor="actualEndDate" className="text-sm">End Date</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal h-8",
                          !formData.actualEndDate && "text-muted-foreground"
                        )}
                      >
                        <CalendarIcon className="mr-1 h-3 w-3" />
                        {formData.actualEndDate ? format(new Date(formData.actualEndDate), "dd MMM yy") : <span>Pick date</span>}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={formData.actualEndDate ? new Date(formData.actualEndDate) : undefined}
                        onSelect={(date) => date && setFormData({ ...formData, actualEndDate: date.toISOString().split("T")[0] })}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
            </div>

            <DrawerFooter className="pt-2">
              <Button type="submit">Create Sub-Integration</Button>
              <DrawerClose asChild>
                <Button variant="outline">Cancel</Button>
              </DrawerClose>
            </DrawerFooter>
          </form>
        </div>
      </DrawerContent>
    </Drawer>
  );
} 