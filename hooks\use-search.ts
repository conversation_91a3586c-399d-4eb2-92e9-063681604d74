import { useState } from 'react';
import { useDebounce } from './use-debounce';

interface UseSearchProps {
  initialSearchTerm?: string;
  debounceTime?: number;
  onSearch?: (term: string) => void;
}

interface UseSearchReturn {
  searchTerm: string;
  searchInput: string;
  setSearchInput: (value: string) => void;
  debouncedSearchTerm: string;
  handleSearch: () => void;
  handleKeyPress: (e: React.KeyboardEvent) => void;
  isSearching: boolean;
  setIsSearching: (value: boolean) => void;
}

/**
 * Custom hook for managing search functionality
 */
export function useSearch({
  initialSearchTerm = '',
  debounceTime = 500,
  onSearch
}: UseSearchProps = {}): UseSearchReturn {
  const [searchTerm, setSearchTerm] = useState(initialSearchTerm);
  const [searchInput, setSearchInput] = useState(initialSearchTerm);
  const [isSearching, setIsSearching] = useState(false);
  
  const debouncedSearchTerm = useDebounce(searchTerm, debounceTime);

  const handleSearch = () => {
    setSearchTerm(searchInput);
    if (onSearch) {
      onSearch(searchInput);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  return {
    searchTerm,
    searchInput,
    setSearchInput,
    debouncedSearchTerm,
    handleSearch,
    handleKeyPress,
    isSearching,
    setIsSearching
  };
}
