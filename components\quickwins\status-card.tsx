"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { QuickWinStatus } from "@/types";

interface StatusCardProps {
  status: QuickWinStatus;
  count: number;
  total: number;
}

const getStatusColor = (status: QuickWinStatus) => {
  const colors: Record<QuickWinStatus, string> = {
    Initiation: "text-pink-500",
    Onboarding: "text-orange-500",
    Live: "text-green-500",
    Hold: "text-gray-500",
  };
  return colors[status] || "text-gray-500";
};

export function StatusCard({ status, count, total }: StatusCardProps) {
  const percentage = ((count / total) * 100).toFixed(1);
  const statusColor = getStatusColor(status);

  return (
    <Card className="shadow-sm hover:shadow-md transition-shadow">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className={`text-2xl font-bold ${statusColor}`}>{status}</CardT<PERSON>le>
        <div className={`text-2xl font-bold ${statusColor}`}>{count}</div>
      </CardHeader>
      <CardContent>
        <div className="text-xs">
          {percentage}% of total integrations
        </div>
      </CardContent>
    </Card>
  );
}