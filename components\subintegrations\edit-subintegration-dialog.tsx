"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>er,
  DrawerClose,
  <PERSON>er<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DrawerFooter,
} from "@/components/ui/drawer";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Trash2, Calendar as CalendarIcon, Check, ChevronsUpDown, Archive } from "lucide-react";
import { useEffect, useState } from "react";
import { SubIntegration, SubIntegrationFlow, SubIntegrationStatus, subIntegrationFlows, subIntegrationStatuses } from "@/types";
import { mutate } from "swr";
import { format } from "date-fns";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";

interface MasterOTA {
  _id: string;
  otaName: string;
  type: string;
  country: string;
  iata: string;
  trueIata: string;
  fop: string;
  clientId: string;
  userId: string;
  contact: string;
}

interface EditSubIntegrationDialogProps {
  subintegration: SubIntegration;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function EditSubIntegrationDialog({
  subintegration,
  open,
  onOpenChange,
}: EditSubIntegrationDialogProps) {
  const [formData, setFormData] = useState({
    batch: '',
    ota: '',
    country: '',
    flow: '' as SubIntegrationFlow,
    iata: '',
    startDate: '',
    weeks: '',
    status: '' as SubIntegrationStatus,
    manager: '',
    actualEndDate: '',
    masterOtaId: '',
    archived: false,
    archivedDate: '',
  });
  const [confirmDelete, setConfirmDelete] = useState(false);
  const [confirmArchive, setConfirmArchive] = useState(false);
  const [masterOtas, setMasterOtas] = useState<MasterOTA[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedMasterOtaId, setSelectedMasterOtaId] = useState<string>('');
  const [otaSearchOpen, setOtaSearchOpen] = useState(false);

  // Fetch master OTAs when the dialog opens
  useEffect(() => {
    if (open) {
      fetchMasterOtas();
    }
  }, [open]);

  const fetchMasterOtas = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/master-otas');
      if (response.ok) {
        const data = await response.json();
        if (Array.isArray(data) && data.length > 0) {
          setMasterOtas(data);
        } else {
          console.error('Received empty or invalid Master OTAs array');
        }
      } else {
        console.error('Failed to fetch master OTAs');
      }
    } catch (error) {
      console.error('Error fetching master OTAs:', error);
    } finally {
      setLoading(false);
    }
  };

  // Initialize form data from subintegration
  useEffect(() => {
    if (subintegration) {
      setFormData({
        batch: subintegration.batch || '',
        ota: subintegration.ota || '',
        country: subintegration.country || '',
        flow: (subintegration.flow as SubIntegrationFlow) || 'Interline',
        iata: subintegration.iata || '',
        startDate: subintegration.startDate 
          ? new Date(subintegration.startDate).toISOString().split('T')[0] 
          : '',
        weeks: subintegration.weeks?.toString() || '',
        status: (subintegration.status as SubIntegrationStatus) || 'Initiation',
        manager: subintegration.manager || '',
        actualEndDate: subintegration.actualEndDate 
          ? new Date(subintegration.actualEndDate).toISOString().split('T')[0]
          : '',
        masterOtaId: typeof subintegration.masterOtaId === 'string' ? subintegration.masterOtaId : subintegration.masterOtaId?._id || '',
        archived: subintegration.archived || false,
        archivedDate: subintegration.archivedDate 
          ? new Date(subintegration.archivedDate).toISOString().split('T')[0]
          : '',
      });
      setSelectedMasterOtaId(typeof subintegration.masterOtaId === 'string' ? subintegration.masterOtaId : subintegration.masterOtaId?._id || '');
    }
  }, [subintegration]);

  const handleOtaSelection = (otaId: string) => {
    setSelectedMasterOtaId(otaId);
    const selectedOta = masterOtas.find(ota => ota._id === otaId);
    if (selectedOta) {
      setFormData({
        ...formData,
        ota: selectedOta.otaName,
        country: selectedOta.country || formData.country,
        iata: selectedOta.iata || formData.iata,
        masterOtaId: otaId,
      });
    }
    setOtaSearchOpen(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!subintegration._id) return;

    try {
      // First update the sub-integration
      const response = await fetch(`/api/subintegrations/${subintegration._id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          weeks: parseInt(formData.weeks),
          masterOtaId: selectedMasterOtaId || undefined,
        }),
      });

      if (response.ok) {
        // If the status is being set to 'Live', update the master OTA's integration type status
        if (formData.status === 'Live' && selectedMasterOtaId) {
          // Map flow types to master OTA integration types
          const flowToIntegrationType: { [key: string]: string } = {
            'Interline': 'interline',
            'Ancillaries': 'ancillaries',
            'Seats': 'seats',
            'Change': 'change',
            'Cancel': 'cancel',
            'Void': 'void',
            'Multi-City': 'multiCity',
            'APIS': 'apis'
          };

          const integrationType = flowToIntegrationType[formData.flow];
          if (integrationType) {
            // Call the master OTA update API
            const masterOtaResponse = await fetch(`/api/master-otas/${selectedMasterOtaId}/update-booking`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                integrationType
              }),
            });

            if (!masterOtaResponse.ok) {
              console.error('Failed to update master OTA integration status');
            }
          }
        }

        onOpenChange(false);
        
        // Refresh all subintegration-related data and master OTA data
        await Promise.all([
          mutate((key) => typeof key === 'string' && key.startsWith('/api/subintegrations')),
          mutate('/api/subintegrations/stats'),
          mutate('/api/master-otas')
        ]);
      } else {
        console.error('Failed to update subintegration');
      }
    } catch (error) {
      console.error('Error updating subintegration:', error);
    }
  };

  const handleDelete = async () => {
    if (!subintegration._id) return;

    try {
      const response = await fetch(`/api/subintegrations/${subintegration._id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setConfirmDelete(false);
        onOpenChange(false);
        
        // Refresh all subintegration-related data
        await Promise.all([
          mutate((key) => typeof key === 'string' && key.startsWith('/api/subintegrations')),
          mutate('/api/subintegrations/stats')
        ]);
      } else {
        console.error('Failed to delete subintegration');
      }
    } catch (error) {
      console.error('Error deleting subintegration:', error);
    }
  };

  const handleArchive = async () => {
    if (!subintegration._id) return;

    try {
      // 1. Archive the sub-integration using the dedicated archive endpoint
      const archiveResponse = await fetch(`/api/subintegrations/${subintegration._id}/archive`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      const archiveResult = await archiveResponse.json();
      
      if (!archiveResponse.ok || !archiveResult.success) {
        console.error('Failed to archive sub-integration:', archiveResult);
        return;
      }

      setConfirmArchive(false);
      onOpenChange(false);
      
      // Refresh all subintegration-related data and master OTA data
      await Promise.all([
        mutate((key) => typeof key === 'string' && key.startsWith('/api/subintegrations')),
        mutate('/api/subintegrations/stats'),
        mutate('/api/master-otas')
      ]);
    } catch (error) {
      console.error('Error archiving sub-integration:', error);
    }
  };

  const formatDate = (date: Date | string | null | undefined) => {
    if (!date) return "N/A";
    try {
      return format(new Date(date), "dd-MMM-yy");
    } catch {
      return "Invalid Date";
    }
  };
  
  // Calculate expected end date based on start date and weeks
  const getExpectedEndDate = () => {
    if (formData.startDate && formData.weeks) {
      const startDate = new Date(formData.startDate);
      const endDate = new Date(startDate);
      endDate.setDate(startDate.getDate() + parseInt(formData.weeks) * 7);
      return formatDate(endDate);
    }
    return "N/A";
  };

  return (
    <>
      <Drawer open={open} onOpenChange={onOpenChange}>
        <DrawerContent>
          <div className="mx-auto w-full max-w-4xl px-6">
            <DrawerHeader>
              <DrawerTitle className="text-2xl font-semibold">Edit Sub-Integration: {subintegration.ota}</DrawerTitle>
            </DrawerHeader>
            
            <form onSubmit={handleSubmit} className="py-4">
              {/* Basic Information */}
              <div className="mb-3 max-w-3xl mx-auto">
                <h3 className="text-base font-semibold mb-1 border-b pb-1">Basic Information</h3>
                <div className="grid grid-cols-4 gap-x-3 gap-y-2">
                  <div className="space-y-1">
                    <Label htmlFor="edit-batch" className="text-sm">Batch ID</Label>
                    <Input
                      id="edit-batch"
                      value={formData.batch}
                      onChange={(e) => setFormData({ ...formData, batch: e.target.value })}
                      required
                      className="h-8"
                    />
                  </div>
                  <div className="space-y-1">
                    <Label htmlFor="otaSelect" className="text-sm font-bold">Select Master OTA</Label>
                    <Popover open={otaSearchOpen} onOpenChange={setOtaSearchOpen}>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          role="combobox"
                          aria-expanded={otaSearchOpen}
                          className="h-8 w-full justify-between bg-muted-foreground/5 border-purple-300"
                        >
                          {selectedMasterOtaId
                            ? masterOtas.find((ota) => ota._id === selectedMasterOtaId)?.otaName || formData.ota
                            : "Select Master OTA"}
                          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-full p-0">
                        <Command>
                          <CommandInput placeholder="Search Master OTA..." className="h-9" />
                          <CommandEmpty>No OTA found.</CommandEmpty>
                          <CommandList>
                            <CommandGroup>
                              {loading ? (
                                <CommandItem disabled>Loading Master OTAs...</CommandItem>
                              ) : masterOtas.length > 0 ? (
                                masterOtas.map((ota) => (
                                  <CommandItem
                                    key={ota._id}
                                    value={ota.otaName}
                                    onSelect={() => handleOtaSelection(ota._id)}
                                  >
                                    <Check
                                      className={cn(
                                        "mr-2 h-4 w-4",
                                        selectedMasterOtaId === ota._id ? "opacity-100" : "opacity-0"
                                      )}
                                    />
                                    {ota.otaName} {ota.country ? `(${ota.country})` : ''}
                                  </CommandItem>
                                ))
                              ) : (
                                <CommandItem disabled>No OTAs found</CommandItem>
                              )}
                            </CommandGroup>
                          </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>
                  </div>
                  <div className="space-y-1">
                    <Label htmlFor="edit-country" className="text-sm">Country</Label>
                    <Input
                      id="edit-country"
                      value={formData.country}
                      onChange={(e) => setFormData({ ...formData, country: e.target.value })}
                      required
                      className="h-8"
                      disabled={!!selectedMasterOtaId}
                    />
                  </div>
                  <div className="space-y-1">
                    <Label htmlFor="edit-flow" className="text-sm">Flow Type</Label>
                    <Select
                      value={formData.flow}
                      onValueChange={(value) => setFormData({ ...formData, flow: value as SubIntegrationFlow })}
                      required
                    >
                      <SelectTrigger id="edit-flow" className="h-8">
                        <SelectValue placeholder="Flow" />
                      </SelectTrigger>
                      <SelectContent>
                        {subIntegrationFlows.map((flow) => (
                          <SelectItem key={flow} value={flow}>
                            {flow}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-1">
                    <Label htmlFor="edit-iata" className="text-sm">IATA Code</Label>
                    <Input
                      id="edit-iata"
                      value={formData.iata}
                      onChange={(e) => setFormData({ ...formData, iata: e.target.value })}
                      required
                      className="h-8"
                      disabled={!!selectedMasterOtaId}
                    />
                  </div>
                  <div className="space-y-1">
                    <Label htmlFor="edit-manager" className="text-sm">Project Manager</Label>
                    <Input
                      id="edit-manager"
                      value={formData.manager}
                      onChange={(e) => setFormData({ ...formData, manager: e.target.value })}
                      required
                      className="h-8"
                    />
                  </div>
                </div>
              </div>
              
              {/* Timeline and Status */}
              <div className="mb-3 max-w-3xl mx-auto">
                <h3 className="text-base font-semibold mb-1 border-b pb-1">Timeline and Status</h3>
                <div className="grid grid-cols-4 gap-x-3 gap-y-2">
                  <div className="space-y-1">
                    <Label htmlFor="edit-status" className="text-sm">Status</Label>
                    <Select
                      value={formData.status}
                      onValueChange={(value) => setFormData({ ...formData, status: value as SubIntegrationStatus })}
                      required
                    >
                      <SelectTrigger id="edit-status" className="h-8">
                        <SelectValue placeholder="Status" />
                      </SelectTrigger>
                      <SelectContent>
                        {subIntegrationStatuses.map((status) => (
                          <SelectItem key={status} value={status}>
                            {status}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-1">
                    <Label htmlFor="edit-startDate" className="text-sm">Start Date</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          id="edit-startDate"
                          variant="outline"
                          className={cn(
                            "w-full justify-start text-left font-normal h-8",
                            !formData.startDate && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-1 h-3 w-3" />
                          {formData.startDate ? format(new Date(formData.startDate), "dd MMM yy") : <span>Pick date</span>}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={formData.startDate ? new Date(formData.startDate) : undefined}
                          onSelect={(date) => date && setFormData({ ...formData, startDate: date.toISOString().split("T")[0] })}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                  <div className="space-y-1">
                    <Label htmlFor="edit-weeks" className="text-sm">Weeks</Label>
                    <Input
                      id="edit-weeks"
                      type="number"
                      min="1"
                      max="52"
                      value={formData.weeks}
                      onChange={(e) => setFormData({ ...formData, weeks: e.target.value })}
                      required
                      className="h-8"
                    />
                  </div>
                  <div className="space-y-1">
                    <Label htmlFor="edit-actualEndDate" className="text-sm">End Date</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          id="edit-actualEndDate"
                          variant="outline"
                          className={cn(
                            "w-full justify-start text-left font-normal h-8",
                            !formData.actualEndDate && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-1 h-3 w-3" />
                          {formData.actualEndDate ? format(new Date(formData.actualEndDate), "dd MMM yy") : <span>Pick date</span>}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={formData.actualEndDate ? new Date(formData.actualEndDate) : undefined}
                          onSelect={(date) => date && setFormData({ ...formData, actualEndDate: date.toISOString().split("T")[0] })}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>
              </div>

              <DrawerFooter className="px-0 pt-2">
                <div className="flex w-full items-center justify-between">
                  <div className="flex gap-2">
                    <Button
                      type="button"
                      variant="destructive"
                      onClick={() => setConfirmDelete(true)}
                      className="flex items-center gap-2 h-9"
                    >
                      <Trash2 className="h-4 w-4" />
                      Delete
                    </Button>
                    <Button
                      type="button"
                      variant="secondary"
                      onClick={() => setConfirmArchive(true)}
                      className="flex items-center gap-2 h-9"
                      disabled={formData.archived}
                    >
                      <Archive className="h-4 w-4" />
                      {formData.archived ? 'Archived' : 'Archive'}
                    </Button>
                  </div>
                  <div className="flex gap-2">
                    <Button type="submit">Update Sub-Integration</Button>
                    <DrawerClose asChild>
                      <Button variant="outline" type="button">Cancel</Button>
                    </DrawerClose>
                  </div>
                </div>
              </DrawerFooter>
            </form>
          </div>
        </DrawerContent>
      </Drawer>

      <AlertDialog open={confirmDelete} onOpenChange={setConfirmDelete}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure you want to delete this sub-integration?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the sub-integration:
              <strong className="block mt-2">{formData.ota} ({formData.flow})</strong>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-red-600 hover:bg-red-700">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <AlertDialog open={confirmArchive} onOpenChange={setConfirmArchive}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure you want to archive this sub-integration?</AlertDialogTitle>
            <AlertDialogDescription>
              This will archive the sub-integration and move it to the archived list:
              <strong className="block mt-2">{formData.ota} ({formData.flow})</strong>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleArchive} className="bg-orange-600 hover:bg-orange-700">
              Archive
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
} 