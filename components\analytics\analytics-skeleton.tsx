import { Skeleton } from "@/components/ui/skeleton";

/**
 * Skeleton loading component for the analytics page
 */
export function AnalyticsSkeleton() {
  return (
    <div className="space-y-6">
      {/* Search Bar Skeleton */}
      <div className="mb-4">
        <Skeleton className="h-10 w-full max-w-sm rounded-md" />
      </div>
      
      {/* Conversion Metrics Skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {[...Array(4)].map((_, i) => (
          <Skeleton key={i} className="h-[100px] w-full rounded-lg" />
        ))}
      </div>

      {/* Stat Cards Skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="space-y-2">
            <Skeleton className="h-[125px] w-full rounded-xl" />
            <Skeleton className="h-4 w-[60%]" />
            <Skeleton className="h-4 w-[40%]" />
          </div>
        ))}
      </div>

      {/* Search Bar Skeleton */}
      <div className="mb-6">
        <Skeleton className="h-10 w-full max-w-sm rounded-md" />
      </div>

      {/* Table Skeleton */}
      <div className="mt-8 bg-card rounded-lg shadow p-6">
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <Skeleton className="h-8 w-[200px]" />
            <Skeleton className="h-10 w-[100px]" />
          </div>
          <div className="space-y-2">
            {[...Array(5)].map((_, i) => (
              <Skeleton key={i} className="h-12 w-full" />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
