"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>er,
  Drawer<PERSON>lose,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>er<PERSON>ooter,
} from "@/components/ui/drawer";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { useState, useEffect } from "react";
import { MasterOTA } from "@/types";
import { mutate } from "swr";
import { TrashIcon } from "lucide-react";

interface EditUsageDialogProps {
  usage: MasterOTA;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

interface IntegrationTypeStatus {
  live: boolean;
  liveDate: Date | null;
  lastUsedDate: Date | null;
}

const defaultIntegrationStatus: IntegrationTypeStatus = {
  live: false,
  liveDate: null,
  lastUsedDate: null
};

type IntegrationFields = keyof Pick<MasterOTA, 'booking' | 'ancillaries' | 'seats' | 'cancel' | 'void' | 'change' | 'interline' | 'multiCity' | 'apis'>;

export function EditUsageDialog({ usage, open, onOpenChange }: EditUsageDialogProps) {
  const [formData, setFormData] = useState<MasterOTA>(usage);
  const [confirmDelete, setConfirmDelete] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    // Ensure all integration types have proper structure
    const updatedUsage = { ...usage };
    integrationTypes.forEach((type) => {
      if (!updatedUsage[type]) {
        updatedUsage[type] = defaultIntegrationStatus;
      }
    });
    setFormData(updatedUsage);
  }, [usage]);

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      const response = await fetch(`/api/usages/${usage._id}`, { 
        method: "DELETE",
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        const text = await response.text();
        throw new Error(`Failed to delete usage: ${text}`);
      }

      setConfirmDelete(false);
      onOpenChange(false);
      mutate("/api/usages"); 
    } catch (error) {
      console.error("Failed to delete usage:", error);
      alert(error instanceof Error ? error.message : 'Failed to delete usage');
    } finally {
      setIsDeleting(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      // Extract only the usage-related fields to update
      const updateData = {
        booking: formData.booking?.live ?? false,
        ancillaries: formData.ancillaries?.live ?? false,
        seats: formData.seats?.live ?? false,
        cancel: formData.cancel?.live ?? false,
        void: formData.void?.live ?? false,
        change: formData.change?.live ?? false,
        interline: formData.interline?.live ?? false,
        multiCity: formData.multiCity?.live ?? false,
        apis: formData.apis?.live ?? false
      };
      
      const response = await fetch(`/api/usages/${usage._id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        throw new Error("Failed to update usage");
      }

      // Refresh the data and close the drawer
      await mutate("/api/usages");
      onOpenChange(false);
    } catch (error) {
      console.error("Error updating usage:", error);
    }
  };

  const handleCheckboxChange = (field: IntegrationFields) => {
    const newLiveStatus = !(formData[field]?.live ?? false);
    
    // Only update local state
    setFormData((prev) => ({
      ...prev,
      [field]: {
        ...(prev[field] || defaultIntegrationStatus),
        live: newLiveStatus,
        liveDate: newLiveStatus ? new Date() : prev[field]?.liveDate || null,
        lastUsedDate: newLiveStatus ? new Date() : prev[field]?.lastUsedDate || null
      }
    }));
  };

  const integrationTypes: IntegrationFields[] = [
    'booking',
    'ancillaries',
    'seats',
    'cancel',
    'void',
    'change',
    'interline',
    'multiCity',
    'apis'
  ];

  return (
    <>
      <Drawer open={open} onOpenChange={onOpenChange}>
        <DrawerContent>
          <div className="mx-auto w-full max-w-4xl px-6">
            <DrawerHeader>
              <DrawerTitle className="text-2xl font-semibold">Edit API Modules: {usage.otaName}</DrawerTitle>
            </DrawerHeader>
            
            <form onSubmit={handleSubmit} className="py-4">
              {/* Basic Information */}
              <div className="mb-5">
                <h3 className="text-base font-semibold mb-2 border-b pb-1">API Module Features</h3>
                <div className="space-y-4">
                  <div className="grid grid-cols-3 gap-x-8 gap-y-4">
                    {integrationTypes.map((key) => (
                      <div key={key} className="flex items-center space-x-2">
                        <Checkbox
                          id={key}
                          checked={formData[key]?.live ?? false}
                          onCheckedChange={() => handleCheckboxChange(key)}
                        />
                        <Label htmlFor={key}>
                          {key.charAt(0).toUpperCase() + key.slice(1)}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              <DrawerFooter className="px-0 pt-2">
                <div className="flex w-full items-center justify-between">
                  <div className="flex gap-2">
                    <Button
                      type="button"
                      variant="destructive"
                      onClick={() => setConfirmDelete(true)}
                      className="flex items-center gap-2 h-9"
                    >
                      <TrashIcon className="h-4 w-4" />
                      Delete
                    </Button>
                  </div>
                  <div className="flex gap-2">
                    <Button type="submit">Update Module</Button>
                    <DrawerClose asChild>
                      <Button variant="outline" type="button">Cancel</Button>
                    </DrawerClose>
                  </div>
                </div>
              </DrawerFooter>
            </form>
          </div>
        </DrawerContent>
      </Drawer>

      <AlertDialog open={confirmDelete} onOpenChange={setConfirmDelete}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the API module for
              <span className="font-semibold"> {usage.otaName}</span>.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? 'Deleting...' : 'Delete Module'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
