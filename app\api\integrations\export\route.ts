import { NextResponse } from 'next/server';
import * as XLSX from 'xlsx';
import { connectDB, Integration } from '@/lib/mongodb';
import { format } from 'date-fns';

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

export async function GET(request: Request) {
  try {
    await connectDB();
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search') || '';

    // Build search query
    const query = search ? {
      $or: [
        { batch: { $regex: search, $options: 'i' } },
        { 'masterOta.otaName': { $regex: search, $options: 'i' } },
        { 'masterOta.country': { $regex: search, $options: 'i' } },
        { status: { $regex: search, $options: 'i' } }
      ]
    } : {};

    // Fetch integrations from MongoDB with populated masterOtaId
    const integrations = await Integration.find(query)
      .populate('masterOtaId', 'otaName type country iata trueIata fop clientId userId contact')
      .lean();

    // Transform data for Excel
    const data = integrations.map(integration => {
      const masterOta = integration.masterOtaId || {};
      const formatDate = (date: Date | null | undefined) => {
        if (!date) return 'N/A';
        try {
          return format(new Date(date), 'dd-MMM-yy');
        } catch {
          return 'N/A';
        }
      };

      return {
        'Batch': integration.batch || 'N/A',
        'OTA': masterOta.otaName || 'N/A',
        'Type': masterOta.type || 'N/A',
        'Country': masterOta.country || 'N/A',
        'IATA': masterOta.iata || 'N/A',
        'True IATA': masterOta.trueIata || 'N/A',
        'Form of Payment': masterOta.fop || 'N/A',
        'Client ID': masterOta.clientId || 'N/A',
        'User ID': masterOta.userId || 'N/A',
        'Contact': masterOta.contact || 'N/A',
        'Start Date': formatDate(integration.startDate),
        'Weeks': integration.weeks || 0,
        'Status': integration.status || 'N/A',
        'Expected End Date': formatDate(integration.expectedEndDate),
        'Actual End Date': formatDate(integration.actualEndDate),
        'Manager': integration.manager || 'N/A',
        'Comments': integration.comments || ''
      };
    });

    // Create workbook and worksheet
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, XLSX.utils.json_to_sheet(data), 'Integrations');

    // Generate buffer
    const buf = XLSX.write(wb, { type: 'buffer', bookType: 'xlsx' });

    // Return the Excel file
    return new NextResponse(buf, {
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': `attachment; filename=integrations.xlsx`
      }
    });

  } catch (error) {
    console.error('Error exporting integrations:', error);
    return NextResponse.json({ error: 'Failed to export integrations data' }, { status: 500 });
  }
} 