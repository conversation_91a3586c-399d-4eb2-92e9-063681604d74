import { NextResponse } from 'next/server';
import { connectDB, Integration } from '@/lib/mongodb';
import mongoose from 'mongoose';

export async function GET(request: Request) {
  try {
    await connectDB();
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search') || '';
    const page = parseInt(searchParams.get('page') || '1');
    const pageSize = parseInt(searchParams.get('pageSize') || '25');
    const skip = (page - 1) * pageSize;

    // Create aggregation pipeline
    const pipeline: mongoose.PipelineStage[] = [];
    
    // Lookup stage for populating masterOtaId
    pipeline.push({
      $lookup: {
        from: 'masterotas', // Collection name may need to be adjusted based on your actual collection name
        localField: 'masterOtaId',
        foreignField: '_id',
        as: 'masterOtaData'
      }
    });
    
    // Unwind the array created by lookup
    pipeline.push({
      $unwind: {
        path: '$masterOtaData',
        preserveNullAndEmptyArrays: true
      }
    });
    
    // Match stage for search if a search term is provided
    if (search) {
      pipeline.push({
        $match: {
          $and: [
            {
              $or: [
                { archived: { $exists: false } },
                { archived: false }
              ]
            },
            {
              $or: [
                { batch: { $regex: search, $options: 'i' } },
                { status: { $regex: search, $options: 'i' } },
                { 'masterOtaData.otaName': { $regex: search, $options: 'i' } },
                { 'masterOtaData.country': { $regex: search, $options: 'i' } },
                { 'masterOtaData.clientId': { $regex: search, $options: 'i' } }
              ]
            }
          ]
        }
      });
    } else {
      // Add match stage for archived filter even when there's no search
      pipeline.push({
        $match: {
          $or: [
            { archived: { $exists: false } },
            { archived: false }
          ]
        }
      });
    }
    
    // Sort by startDate
    pipeline.push({
      $sort: { startDate: 1 }
    } as mongoose.PipelineStage);
    
    // Get total count before pagination
    const countPipeline = [...pipeline];
    countPipeline.push({ $count: 'total' } as mongoose.PipelineStage);
    
    // Add pagination
    pipeline.push({ $skip: skip } as mongoose.PipelineStage);
    pipeline.push({ $limit: pageSize } as mongoose.PipelineStage);
    
    // Execute queries
    const [results, countResult] = await Promise.all([
      Integration.aggregate(pipeline),
      Integration.aggregate(countPipeline)
    ]);
    
    const total = countResult.length > 0 ? countResult[0].total : 0;
    const totalPages = Math.ceil(total / pageSize);
    
    // Process results to match the expected format
    const currentDate = new Date();
    const integrationsWithCalculatedFields = results.map((integration) => {
      const startDate = new Date(integration.startDate);
      const endDate = integration.actualEndDate 
        ? new Date(integration.actualEndDate) 
        : currentDate;
      
      const timeDifference = endDate.getTime() - startDate.getTime();
      const weeksElapsed = Math.max(0, Math.round((timeDifference / (1000 * 60 * 60 * 24 * 7)) * 10) / 10);
      
      const expectedEndDate = new Date(startDate);
      expectedEndDate.setDate(expectedEndDate.getDate() + (integration.weeks * 7));

      // Get the masterOta data from the lookup result
      const masterOta = integration.masterOtaData || {};
      
      return {
        ...integration,
        masterOtaId: integration.masterOtaId, // Preserve the original ID reference
        masterOtaData: undefined, // Remove the expanded data to match original format
        weeksElapsed,
        expectedEndDate,
        // Map MasterOTA fields to the integration level with fallbacks
        ota: masterOta.otaName || 'N/A',
        type: masterOta.type || 'N/A',
        country: masterOta.country || 'N/A',
        iata: masterOta.iata || 'N/A',
        trueIata: masterOta.trueIata || 'N/A',
        fop: masterOta.fop || 'N/A',
        clientId: masterOta.clientId || 'N/A',
        userId: masterOta.userId || 'N/A',
        contact: masterOta.contact || 'N/A'
      };
    });

    return NextResponse.json({
      data: integrationsWithCalculatedFields,
      total,
      totalPages
    });
  } catch (error) {
    console.error('Error fetching integrations:', error);
    return NextResponse.json({ error: 'Failed to fetch integrations' }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    await connectDB();
    const data = await request.json();
    
    // Validate required fields
    if (!data.masterOtaId) {
      return NextResponse.json({ error: 'masterOtaId is required' }, { status: 400 });
    }
    
    // Dynamically calculate weeksElapsed and expectedEndDate
    const startDate = new Date(data.startDate);
    
    // Calculate expectedEndDate
    const expectedEndDate = new Date(startDate);
    expectedEndDate.setDate(expectedEndDate.getDate() + (data.weeks * 7));
    
    // Calculate weeksElapsed
    const currentDate = new Date();
    const endDate = data.actualEndDate && !isNaN(Date.parse(data.actualEndDate))
      ? new Date(data.actualEndDate)
      : currentDate;
    
    const timeDifference = endDate.getTime() - startDate.getTime();
    const weeksElapsed = Math.round((timeDifference / (1000 * 60 * 60 * 24 * 7)) * 10) / 10;
    
    // Create integration with only the fields in our schema
    const integrationData = {
      batch: data.batch,
      ota: data.ota,
      masterOtaId: data.masterOtaId,
      startDate,
      expectedEndDate,
      weeksElapsed,
      weeks: parseInt(data.weeks),
      status: data.status || 'Initiation',
      manager: data.manager || '',
      actualEndDate: data.actualEndDate && !isNaN(Date.parse(data.actualEndDate)) ? new Date(data.actualEndDate) : null,
      comments: data.comments || '',
      archived: false
    };
    
    const integration = new Integration(integrationData);
    await integration.save();
    
    return NextResponse.json(integration, { status: 201 });
  } catch (error) {
    console.error('Error creating integration:', error);
    return NextResponse.json({ error: 'Failed to create integration' }, { status: 500 });
  }
} 