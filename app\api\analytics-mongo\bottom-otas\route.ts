import { NextResponse } from "next/server";
import { connectDB, Looks, Books } from "../../../../lib/mongodb";

export const runtime = "nodejs";
export const dynamic = 'force-dynamic';

export async function GET() {
  try {
    // Connect to MongoDB
    await connectDB();
    
    // Aggregate booking data by OTA
    const bookingsByOta = await Books.aggregate([
      {
        $group: {
          _id: "$otaName",
          totalBookings: { $sum: "$bookingCount" }
        }
      },
      {
        $sort: { totalBookings: 1 } // Sort in ascending order
      }
    ]);
    
    // Format bookings data
    const otaBookings: { [key: string]: number } = {};
    bookingsByOta.forEach((item) => {
      otaBookings[item._id] = item.totalBookings;
    });
    
    // Calculate total bookings across all OTAs
    const totalBookings = Object.values(otaBookings).reduce((sum, count) => sum + (count || 0), 0);
    
    // Aggregate looks data by OTA
    const looksByOta = await Looks.aggregate([
      {
        $group: {
          _id: "$otaName",
          Success: { $sum: "$success" },
          WrongRoutes: { $sum: "$wrongRoutes" },
          Throttled: { $sum: "$throttled" },
          Failures: { $sum: "$failures" }
        }
      }
    ]);
    
    // Format looks data
    const otaLooks: { [key: string]: { Success: number; WrongRoutes: number; Throttled: number; Failures: number } } = {};
    looksByOta.forEach((item) => {
      otaLooks[item._id] = {
        Success: item.Success || 0,
        WrongRoutes: item.WrongRoutes || 0,
        Throttled: item.Throttled || 0,
        Failures: item.Failures || 0
      };
    });
      // Define meta search engines to exclude
    const metaSearchEnginesToExclude = [
      'GOOGLE_FZRES_OTA_P',
      'Skyscanner_FZ_P',
      'WEGO_FZRES_OTA_P',
      'INFARE_FZRES_AFF_P',
      'AVIASALES_FZ_P',
      'Kayak_FZ_P'
    ];
    
    // Combine all OTAs from both datasets, excluding meta search engines
    const allOtas = [
      ...Object.keys(otaBookings),
      ...Object.keys(otaLooks)
    ]
    .filter((value, index, self) => self.indexOf(value) === index)
    .filter(otaName => !metaSearchEnginesToExclude.includes(otaName));
    
    // Calculate and format the response data
    const otaData = allOtas.map((otaName) => {
      const lookData = otaLooks[otaName] || { Success: 0, WrongRoutes: 0, Throttled: 0, Failures: 0 };
      const bookings = otaBookings[otaName] || 0;
      
      // Calculate totals and rates
      const totalLooks = lookData.Success + lookData.WrongRoutes + lookData.Throttled + lookData.Failures;
      const lookToBook = bookings > 0 ? Math.round(lookData.Success / bookings) : 0; // Only use Success for LookToBook
      const bookingPercentage = totalBookings > 0 ? (bookings / totalBookings) * 100 : 0;
      
      // Calculate rates
      const successRate = totalLooks > 0 ? (lookData.Success / totalLooks) * 100 : 0;
      const wrongRoutesRate = totalLooks > 0 ? (lookData.WrongRoutes / totalLooks) * 100 : 0;
      const throttledRate = totalLooks > 0 ? (lookData.Throttled / totalLooks) * 100 : 0;
      const failureRate = totalLooks > 0 ? (lookData.Failures / totalLooks) * 100 : 0;
      
      // Format the OTA name - capitalize first letter if needed
      const formattedOtaName = otaName.includes('_') ? otaName : otaName.charAt(0).toUpperCase() + otaName.slice(1);
      
      return {
        OTAName: formattedOtaName,
        TotalLooks: lookData.Success, // Using only Success as TotalLooks to match original behavior
        TotalBookings: bookings,
        LookToBook: lookToBook,
        BookingPercentage: bookingPercentage.toFixed(2),
        SuccessRate: parseFloat(successRate.toFixed(1)),
        WrongRoutesRate: parseFloat(wrongRoutesRate.toFixed(1)),
        ThrottledRate: parseFloat(throttledRate.toFixed(1)),
        FailureRate: parseFloat(failureRate.toFixed(1))
      };
    });
      // Sort by bookings (lowest first) and then by looks (highest first) when bookings are equal
    const bottomOtas = otaData
      .sort((a, b) => {
        // First sort by bookings ascending
        const bookingDiff = a.TotalBookings - b.TotalBookings;
        
        // If bookings are the same, sort by looks descending
        if (bookingDiff === 0) {
          return b.TotalLooks - a.TotalLooks; // Higher looks first when bookings are equal
        }
        
        return bookingDiff; // Otherwise sort by bookings (lowest first)
      })
      .slice(0, 25);
    
    return NextResponse.json({
      success: true,
      data: bottomOtas
    });
  } catch (error) {
    console.error("Error fetching bottom OTAs data from MongoDB:", error);
    return NextResponse.json({ success: false, error: (error as Error).message }, { status: 500 });
  }
}
