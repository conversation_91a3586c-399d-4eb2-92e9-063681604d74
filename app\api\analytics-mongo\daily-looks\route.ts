import { NextResponse } from "next/server";
import { connectDB, Looks } from "../../../../lib/mongodb";
import { WorkflowIcon } from "lucide-react";

export const runtime = "nodejs";
export const dynamic = 'force-dynamic';

// Helper function to format date for display
const formatDate = (date: Date): string => {
  const day = date.getDate().toString().padStart(2, '0');
  const month = date.toLocaleString('en-US', { month: 'short' });
  const year = date.getFullYear().toString().substring(2);
  return `${day}-${month}-${year}`;
};

export async function GET(request: Request) {
  try {
    const url = new URL(request.url);
    const search = url.searchParams.get("search") || "";
    
    // Connect to MongoDB
    await connectDB();
    
    // Create base query conditions
    const baseQuery = search ? {
      $or: [
        { otaName: { $regex: search, $options: 'i' } },
        ...(search.match(/^\d{1,2}[-\/]\w+[-\/]\d{2,4}$/) ? [{
          date: {
            $gte: new Date(search),
            $lt: new Date(new Date(search).setDate(new Date(search).getDate() + 1))
          }
        }] : [])
      ]
    } : {};

    // Check if it's an OTA search
    const isOtaSearch = search && !search.match(/^\d{1,2}[-\/]\w+[-\/]\d{2,4}$/);

    // Use aggregation pipeline for efficient data processing
    const aggregationPipeline = [
      { $match: baseQuery },
      {
        $group: {
          _id: {
            date: "$date",
            ...(isOtaSearch ? { otaName: "$otaName" } : {})
          },
          Success: { $sum: { $ifNull: ["$success", 0] } },
          WrongRoutes: { $sum: { $ifNull: ["$wrongRoutes", 0] } },
          Throttled: { $sum: { $ifNull: ["$throttled", 0] } },
          Failures: { $sum: { $ifNull: ["$failures", 0] } }
        }
      },
      {
        $project: {
          _id: 0,
          date: "$_id.date",
          ...(isOtaSearch ? { OTAName: "$_id.otaName" } : {}),
          Success: 1,
          WrongRoutes: 1,
          Throttled: 1,
          Failures: 1
        }
      },
      { $sort: { date: 1 as const } }
    ];

    const response = await Looks.aggregate(aggregationPipeline);
    
    // Transform the response to rename fields if needed
    const transformedResponse = response.map((item) => {
      // Format the date as dd-MMM-yy
      let formattedDate = item.date;
      
      // Create a new object with the correct field names
      return {
        date: formattedDate,
        Success: item.Success,
        WrongRoutes: item.WrongRoutes,
        Throttled: item.Throttled,
        Failures: item.Failures
      };
    });
    
    return NextResponse.json({ 
      success: true, 
      data: transformedResponse,
      searchTerm: search || undefined
    });
  } catch (error) {
    console.error("Error fetching daily looks data from MongoDB:", error);
    return NextResponse.json({ success: false, error: (error as Error).message }, { status: 500 });
  }
} 