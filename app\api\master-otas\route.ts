import { NextResponse } from 'next/server';
import { connectDB, MasterOTA, ActivityLog } from '@/lib/mongodb';

// GET all Master OTAs
export async function GET() {
  try {
    await connectDB();
    const masterOtas = await MasterOTA.find({}).sort({ otaName: 1 });
    return NextResponse.json(masterOtas, { status: 200 });
  } catch (error) {
    console.error('Error getting master OTAs:', error);
    return NextResponse.json({ error: 'Failed to get master OTAs' }, { status: 500 });
  }
}

// POST to create a new Master OTA
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { otaName, type, country, iata, trueIata, fop, clientId, userId, techPartner, contact } = body;

    // Debug logging
    console.log('Received POST data:', { 
      otaName, type, country, iata, trueIata, fop, clientId, userId, techPartner, contact 
    });

    await connectDB();
    
    // Check if OTA with same name already exists
    const existingOta = await MasterOTA.findOne({ otaName });
    if (existingOta) {
      return NextResponse.json({ error: 'OTA with this name already exists' }, { status: 409 });
    }

    // Create new Master OTA
    const newMasterOta = await MasterOTA.create({
      otaName: otaName,
      type: type,
      country: country,
      iata: iata,
      trueIata: trueIata,
      fop: fop,
      clientId: clientId,
      userId: userId,
      techPartner: techPartner,
      contact: contact
    });

    console.log('Created MasterOTA document:', newMasterOta);

    // Log activity
    await ActivityLog.create({
      action: 'Create Master OTA',
      details: `Created master OTA: ${otaName}`,
      performedBy: 'System', // Ideally this would be the current user
      timestamp: new Date()
    });

    return NextResponse.json(newMasterOta, { status: 201 });
  } catch (error) {
    console.error('Error creating master OTA:', error);
    return NextResponse.json({ error: 'Failed to create master OTA' }, { status: 500 });
  }
} 