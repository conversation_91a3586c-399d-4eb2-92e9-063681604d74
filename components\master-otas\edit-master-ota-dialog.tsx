'use client';

import { useEffect, useState } from 'react';
import { MasterOTA } from '@/types';
import { Button } from '@/components/ui/button';
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from '@/components/ui/drawer';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';

interface EditMasterOtaDialogProps {
  masterOta: MasterOTA;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export function EditMasterOtaDialog({ 
  masterOta, 
  open, 
  onOpenChange, 
  onSuccess 
}: EditMasterOtaDialogProps) {
  const [formData, setFormData] = useState<Partial<MasterOTA>>({});
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (masterOta) {
      setFormData({
        otaName: masterOta.otaName,
        type: masterOta.type,
        country: masterOta.country,
        iata: masterOta.iata,
        trueIata: masterOta.trueIata,
        fop: masterOta.fop,
        clientId: masterOta.clientId,
        userId: masterOta.userId,
        techPartner: masterOta.techPartner,
        contact: masterOta.contact,
      });
    }
  }, [masterOta]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setIsLoading(true);
      
      const response = await fetch(`/api/master-otas/${masterOta._id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update Master OTA');
      }
      
      toast.success('Master OTA updated successfully');
      onOpenChange(false);
      
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error updating Master OTA:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to update Master OTA');
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <Drawer open={open} onOpenChange={onOpenChange} direction="right">
      <DrawerContent className="right-0 left-auto w-[450px] h-full">
        <div className="mx-auto w-full p-6">
          <DrawerHeader className="px-0">
            <DrawerTitle className="text-2xl font-semibold">Edit Master OTA</DrawerTitle>
          </DrawerHeader>
          <form onSubmit={handleSubmit} className="py-4 space-y-4">
            <div className="flex flex-col space-y-1.5">
              <Label htmlFor="otaName">OTA Name *</Label>
              <Input
                id="otaName"
                name="otaName"
                value={formData.otaName || ''}
                onChange={handleInputChange}
                required
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="flex flex-col space-y-1.5">
                <Label htmlFor="type">Type</Label>
                <select
                  id="type"
                  name="type"
                  value={formData.type || 'B2B'}
                  onChange={handleInputChange}
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  required
                >
                  <option value="B2B">B2B</option>
                  <option value="B2C">B2C</option>
                  <option value="PP">PP</option>
                  <option value="Meta">Meta</option>
                </select>
              </div>
              <div className="flex flex-col space-y-1.5">
                <Label htmlFor="country">Country</Label>
                <Input
                  id="country"
                  name="country"
                  value={formData.country || ''}
                  onChange={handleInputChange}
                />
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="flex flex-col space-y-1.5">
                <Label htmlFor="iata">IATA</Label>
                <Input
                  id="iata"
                  name="iata"
                  value={formData.iata || ''}
                  onChange={handleInputChange}
                />
              </div>
              <div className="flex flex-col space-y-1.5">
                <Label htmlFor="trueIata">True IATA</Label>
                <Input
                  id="trueIata"
                  name="trueIata"
                  value={formData.trueIata || ''}
                  onChange={handleInputChange}
                />
              </div>
            </div>
            
            <div className="flex flex-col space-y-1.5">
              <Label htmlFor="fop">Form of Payment</Label>
              <select
                id="fop"
                name="fop"
                value={formData.fop || 'INVC'}
                onChange={handleInputChange}
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                required
              >
                <option value="INVC">INVC</option>
                <option value="WBSP">WBSP</option>
              </select>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="flex flex-col space-y-1.5">
                <Label htmlFor="clientId">Client ID</Label>
                <Input
                  id="clientId"
                  name="clientId"
                  value={formData.clientId || ''}
                  onChange={handleInputChange}
                />
              </div>
              <div className="flex flex-col space-y-1.5">
                <Label htmlFor="userId">User ID</Label>
                <Input
                  id="userId"
                  name="userId"
                  value={formData.userId || ''}
                  onChange={handleInputChange}
                />
              </div>
            </div>
            
            <div className="flex flex-col space-y-1.5">
              <Label htmlFor="techPartner">Tech Partner</Label>
              <Input
                id="techPartner"
                name="techPartner"
                value={formData.techPartner || ''}
                onChange={handleInputChange}
              />
            </div>
            
            <div className="flex flex-col space-y-1.5">
              <Label htmlFor="contact">Contact</Label>
              <Textarea
                id="contact"
                name="contact"
                value={formData.contact || ''}
                onChange={handleInputChange}
                className="min-h-[100px] resize-y"
                placeholder="Enter contact information"
              />
            </div>
            
            <DrawerFooter className="px-0 pt-2">
              <Button type="submit" disabled={isLoading}>
                {isLoading ? 'Saving...' : 'Save Changes'}
              </Button>
              <DrawerClose asChild>
                <Button variant="outline" type="button">Cancel</Button>
              </DrawerClose>
            </DrawerFooter>
          </form>
        </div>
      </DrawerContent>
    </Drawer>
  );
} 