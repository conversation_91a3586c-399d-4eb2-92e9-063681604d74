import { NextResponse } from 'next/server';
import { connectDB, SubIntegration, MasterOTA } from '@/lib/mongodb';
import mongoose from 'mongoose';

export const runtime = 'nodejs';

/**
 * POST endpoint specifically for archiving sub-integrations
 * Updates the archived flag and archivedDate, and updates master OTA integration type status
 */
export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB();
    
    console.log('Archiving sub-integration with ID:', params.id);

    // Validate and convert the ID to ObjectId
    let objectId;
    try {
      objectId = new mongoose.Types.ObjectId(params.id);
    } catch (error) {
      return NextResponse.json({ error: 'Invalid ID format' }, { status: 400 });
    }
    
    // First, get the sub-integration to get its masterOtaId, flow, and actualEndDate
    const subintegration = await SubIntegration.findById(objectId);
    if (!subintegration) {
      return NextResponse.json({ error: 'Sub-integration not found' }, { status: 404 });
    }

    // Get current date for archive timestamp
    const archivedDate = new Date();
    
    // Update the sub-integration using findByIdAndUpdate
    const result = await SubIntegration.findByIdAndUpdate(
      objectId,
      { 
        $set: { 
          archived: true,
          archivedDate: archivedDate
        } 
      },
      { new: true }
    );
    
    console.log('Archive result:', result);
    
    if (!result) {
      return NextResponse.json({ error: 'Sub-integration not found' }, { status: 404 });
    }

    // If the sub-integration has a masterOtaId, update its usage data
    if (subintegration.masterOtaId) {
      // Map flow types to master OTA integration types
      const flowToIntegrationType: { [key: string]: string } = {
        'Interline': 'interline',
        'Ancillaries': 'ancillaries',
        'Seats': 'seats',
        'Change': 'change',
        'Cancel': 'cancel',
        'Void': 'void',
        'Multi-City': 'multiCity',
        'APIS': 'apis'
      };

      const integrationType = flowToIntegrationType[subintegration.flow];
      if (integrationType) {
        // Create update object for master OTA
        // Set the entire integration type object structure
        const usageUpdate = {
          [`${integrationType}`]: {
            live: true,
            liveDate: subintegration.actualEndDate || archivedDate,
            lastUsedDate: null
          }
        };

        // Convert masterOtaId to ObjectId if it's a string
        const masterOtaId = typeof subintegration.masterOtaId === 'string' 
          ? new mongoose.Types.ObjectId(subintegration.masterOtaId)
          : subintegration.masterOtaId;

        // Update the master OTA using findByIdAndUpdate
        await MasterOTA.findByIdAndUpdate(
          masterOtaId,
          { $set: usageUpdate },
          { new: true }
        );
      }
    }
    
    return NextResponse.json({ 
      success: true, 
      message: 'Sub-integration archived successfully',
      data: result
    });
  } catch (error) {
    console.error('Error archiving sub-integration:', error);
    return NextResponse.json({ error: 'Failed to archive sub-integration' }, { status: 500 });
  }
} 