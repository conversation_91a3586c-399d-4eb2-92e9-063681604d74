import { NextResponse } from 'next/server';
import { connectDB, MasterOTA, ActivityLog } from '@/lib/mongodb';

// GET Master OTA by ID
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB();
    const masterOta = await MasterOTA.findById(params.id);
    
    if (!masterOta) {
      return NextResponse.json({ error: 'Master OTA not found' }, { status: 404 });
    }
    
    return NextResponse.json(masterOta, { status: 200 });
  } catch (error) {
    console.error('Error getting master OTA:', error);
    return NextResponse.json({ error: 'Failed to get master OTA' }, { status: 500 });
  }
}

// PUT to update Master OTA
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    const { otaName, type, country, iata, trueIata, fop, clientId, userId, techPartner, contact } = body;

    await connectDB();
    
    // Check if OTA exists
    const existingOta = await MasterOTA.findById(params.id);
    if (!existingOta) {
      return NextResponse.json({ error: 'Master OTA not found' }, { status: 404 });
    }

    // Check if new name conflicts with another OTA (if name is being changed)
    if (otaName && otaName !== existingOta.otaName) {
      const nameConflict = await MasterOTA.findOne({ otaName, _id: { $ne: params.id } });
      if (nameConflict) {
        return NextResponse.json({ error: 'Another OTA with this name already exists' }, { status: 409 });
      }
    }

    // Update Master OTA
    const updatedMasterOta = await MasterOTA.findByIdAndUpdate(
      params.id,
      {
        otaName,
        type,
        country,
        iata,
        trueIata,
        fop,
        clientId,
        userId,
        techPartner,
        contact
      },
      { new: true, runValidators: true }
    );

    // Log activity
    await ActivityLog.create({
      action: 'Update Master OTA',
      details: `Updated master OTA: ${updatedMasterOta.otaName}`,
      performedBy: 'System', // Ideally this would be the current user
      timestamp: new Date()
    });

    return NextResponse.json(updatedMasterOta, { status: 200 });
  } catch (error) {
    console.error('Error updating master OTA:', error);
    return NextResponse.json({ error: 'Failed to update master OTA' }, { status: 500 });
  }
}

// DELETE Master OTA
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB();
    
    // Check if OTA exists
    const existingOta = await MasterOTA.findById(params.id);
    if (!existingOta) {
      return NextResponse.json({ error: 'Master OTA not found' }, { status: 404 });
    }

    // Delete Master OTA
    await MasterOTA.findByIdAndDelete(params.id);

    // Log activity
    await ActivityLog.create({
      action: 'Delete Master OTA',
      details: `Deleted master OTA: ${existingOta.otaName}`,
      performedBy: 'System', // Ideally this would be the current user
      timestamp: new Date()
    });

    return NextResponse.json({ message: 'Master OTA deleted successfully' }, { status: 200 });
  } catch (error) {
    console.error('Error deleting master OTA:', error);
    return NextResponse.json({ error: 'Failed to delete master OTA' }, { status: 500 });
  }
} 