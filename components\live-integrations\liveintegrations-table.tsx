'use client';

import { useState, useEffect, useMemo } from 'react';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';
import { format } from 'date-fns';
import { Download, Search, Filter } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { TablePagination } from '@/components/ui/table-pagination';

interface LiveIntegration {
  _id: string;
  ota: string;
  batch: string;
  type: string;
  country: string;
  status: string;
  manager: string;
  archivedDate: string;
  clientId?: string;
  userId?: string;
  actualEndDate?: string;
  sourceType: 'Integration' | 'QuickWin';
}

interface ApiResponse {
  data: LiveIntegration[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

interface Filters {
  batch?: string;
  type?: string;
  status?: string;
  country?: string;
  manager?: string;
  sourceType?: string;
}

interface LiveIntegrationsTableProps {
  initialData?: ApiResponse;
}

const ALL_VALUE = "all";

export default function LiveIntegrationsTable({ initialData }: LiveIntegrationsTableProps) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  
  const [data, setData] = useState<ApiResponse | null>(initialData || null);
  const [loading, setLoading] = useState(!initialData);
  const [searchTerm, setSearchTerm] = useState(searchParams.get('search') || '');
  const [filters, setFilters] = useState<Filters>({});

  const currentPage = Number(searchParams.get('page')) || 1;
  const pageSize = Number(searchParams.get('pageSize')) || 25;
  const activeSearch = searchParams.get('search') || '';

  useEffect(() => {
    // Only fetch if we don't have initialData or if the search params have changed
    if (initialData && !searchParams.toString()) {
      return;
    }
    
    const fetchData = async () => {
      setLoading(true);
      try {
        const params = new URLSearchParams(searchParams.toString());
        
        const response = await fetch(`/api/live-integrations?${params}`);
        const json = await response.json();
        setData(json);
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [searchParams, initialData]);

  const createQueryString = (params: Record<string, string | null>) => {
    const newParams = new URLSearchParams(searchParams.toString());
    
    Object.entries(params).forEach(([key, value]) => {
      if (value === null) {
        newParams.delete(key);
      } else {
        newParams.set(key, value);
      }
    });
    
    return newParams.toString();
  };

  const handleSearch = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      const query = createQueryString({ 
        search: searchTerm, 
        page: '1' 
      });
      router.push(`${pathname}?${query}`);
    }
  };

  const handleExport = async () => {
    try {
      const params = new URLSearchParams({
        export: 'true',
        ...Object.fromEntries(searchParams)
      });
      const response = await fetch(`/api/live-integrations?${params}`);
      if (!response.ok) throw new Error('Export failed');
      
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'live-integrations.xlsx';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error exporting live integrations:', error);
    }
  };

  const handleFilterChange = (key: keyof Filters, value: string | null) => {
    const newValue = value === ALL_VALUE ? null : value;
    setFilters(prev => ({
      ...prev,
      [key]: newValue
    }));
    
    const query = createQueryString({ 
      [key]: newValue, 
      page: '1' 
    });
    router.push(`${pathname}?${query}`);
  };

  const uniqueBatches = useMemo<string[]>(() => 
    Array.from(new Set((data?.data?.map((i: LiveIntegration) => i.batch) || []) as string[])).sort(),
    [data?.data]
  );

  const uniqueTypes = useMemo<string[]>(() => 
    Array.from(new Set((data?.data?.map((i: LiveIntegration) => i.type) || []) as string[])).sort(),
    [data?.data]
  );

  const uniqueStatuses = useMemo<string[]>(() => 
    Array.from(new Set((data?.data?.map((i: LiveIntegration) => i.status) || []) as string[])).sort(),
    [data?.data]
  );

  const uniqueCountries = useMemo<string[]>(() => 
    Array.from(new Set((data?.data?.map((i: LiveIntegration) => i.country) || []) as string[])).sort(),
    [data?.data]
  );

  const uniqueManagers = useMemo<string[]>(() => 
    Array.from(new Set((data?.data?.map((i: LiveIntegration) => i.manager) || []) as string[])).sort(),
    [data?.data]
  );

  const uniqueSourceTypes = useMemo<string[]>(() => 
    Array.from(new Set((data?.data?.map((i: LiveIntegration) => i.sourceType) || []) as string[])).sort(),
    [data?.data]
  );

  const filteredData = useMemo(() => {
    if (!data?.data) return [];
    
    return data.data.filter((integration: LiveIntegration) => {
      // Apply client-side filters for any filters present in URL params
      const batchFilter = searchParams.get('batch');
      if (batchFilter && integration.batch !== batchFilter) return false;
      
      const sourceTypeFilter = searchParams.get('sourceType');
      if (sourceTypeFilter && integration.sourceType !== sourceTypeFilter) return false;
      
      const typeFilter = searchParams.get('type');
      if (typeFilter && integration.type !== typeFilter) return false;
      
      const statusFilter = searchParams.get('status');
      if (statusFilter && integration.status !== statusFilter) return false;
      
      const countryFilter = searchParams.get('country');
      if (countryFilter && integration.country !== countryFilter) return false;
      
      const managerFilter = searchParams.get('manager');
      if (managerFilter && integration.manager !== managerFilter) return false;
      
      return true;
    });
  }, [data?.data, searchParams]);

  const handlePageChange = (page: number) => {
    const query = createQueryString({ page: page.toString() });
    router.push(`${pathname}?${query}`);
  };

  if (loading) return <div className="text-center py-4">Loading...</div>;
  if (!data) return <div className="text-center py-4 text-red-500">No data available</div>;

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search live integrations... (Press Enter to search)"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onKeyDown={handleSearch}
            className="pl-10"
          />
        </div>
        <Button
          onClick={handleExport}
          className="flex items-center gap-2"
          variant="outline"
        >
          <Download className="h-4 w-4" />
          <span>Export</span>
        </Button>
      </div>

      <div className="rounded-md border">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b bg-muted/50">
                <th className="h-12 px-4 text-left align-middle font-semibold text-sm text-gray-500">
                  <div className="flex items-center gap-2">
                    Batch
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-6 w-6">
                          <Filter className="h-4 w-4" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-48">
                        <Select
                          value={searchParams.get('batch') || ALL_VALUE}
                          onValueChange={(value) => handleFilterChange('batch', value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select batch" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value={ALL_VALUE}>All Batches</SelectItem>
                            {uniqueBatches.map((batch) => (
                              <SelectItem key={batch} value={batch}>
                                {batch}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </PopoverContent>
                    </Popover>
                  </div>
                </th>
                <th className="h-12 px-4 text-left align-middle font-semibold text-sm text-gray-500">OTA</th>
                <th className="h-12 px-4 text-left align-middle font-semibold text-sm text-gray-500">
                  <div className="flex items-center gap-2">
                    Source Type
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-6 w-6">
                          <Filter className="h-4 w-4" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-48">
                        <Select
                          value={searchParams.get('sourceType') || ALL_VALUE}
                          onValueChange={(value) => handleFilterChange('sourceType', value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select source type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value={ALL_VALUE}>All Sources</SelectItem>
                            {uniqueSourceTypes.map((sourceType) => (
                              <SelectItem key={sourceType} value={sourceType}>
                                {sourceType}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </PopoverContent>
                    </Popover>
                  </div>
                </th>
                <th className="h-12 px-4 text-left align-middle font-semibold text-sm text-gray-500">
                  <div className="flex items-center gap-2">
                    Type
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-6 w-6">
                          <Filter className="h-4 w-4" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-48">
                        <Select
                          value={searchParams.get('type') || ALL_VALUE}
                          onValueChange={(value) => handleFilterChange('type', value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value={ALL_VALUE}>All Types</SelectItem>
                            {uniqueTypes.map((type) => (
                              <SelectItem key={type} value={type}>
                                {type}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </PopoverContent>
                    </Popover>
                  </div>
                </th>
                <th className="h-12 px-4 text-left align-middle font-semibold text-sm text-gray-500">
                  <div className="flex items-center gap-2">
                    Country
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-6 w-6">
                          <Filter className="h-4 w-4" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-48">
                        <Select
                          value={searchParams.get('country') || ALL_VALUE}
                          onValueChange={(value) => handleFilterChange('country', value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select country" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value={ALL_VALUE}>All Countries</SelectItem>
                            {uniqueCountries.map((country) => (
                              <SelectItem key={country} value={country}>
                                {country}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </PopoverContent>
                    </Popover>
                  </div>
                </th>
                <th className="h-12 px-4 text-left align-middle font-semibold text-sm text-gray-500">
                  <div className="flex items-center gap-2">
                    Status
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-6 w-6">
                          <Filter className="h-4 w-4" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-48">
                        <Select
                          value={searchParams.get('status') || ALL_VALUE}
                          onValueChange={(value) => handleFilterChange('status', value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value={ALL_VALUE}>All Statuses</SelectItem>
                            {uniqueStatuses.map((status) => (
                              <SelectItem key={status} value={status}>
                                {status}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </PopoverContent>
                    </Popover>
                  </div>
                </th>
                <th className="h-12 px-4 text-left align-middle font-semibold text-sm text-gray-500">
                  <div className="flex items-center gap-2">
                    Manager
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-6 w-6">
                          <Filter className="h-4 w-4" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-48">
                        <Select
                          value={searchParams.get('manager') || ALL_VALUE}
                          onValueChange={(value) => handleFilterChange('manager', value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select manager" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value={ALL_VALUE}>All Managers</SelectItem>
                            {uniqueManagers.map((manager) => (
                              <SelectItem key={manager} value={manager}>
                                {manager}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </PopoverContent>
                    </Popover>
                  </div>
                </th>
                <th className="h-12 px-4 text-left align-middle font-semibold text-sm text-gray-500">Client ID</th>
                <th className="h-12 px-4 text-left align-middle font-semibold text-sm text-gray-500">User ID</th>
                <th className="h-12 px-4 text-left align-middle font-semibold text-sm text-gray-500">Live Date</th>
              </tr>
            </thead>
            <tbody>
              {filteredData.map((item) => (
                <tr key={item._id} className="border-b">
                  <td className="p-4 text-sm">{item.batch}</td>
                  <td className="p-4 text-sm">{item.ota}</td>
                  <td className="p-4 text-sm">{item.sourceType}</td>
                  <td className="p-4 text-sm">{item.type}</td>
                  <td className="p-4 text-sm">{item.country}</td>
                  <td className="p-4 text-sm">{item.status}</td>
                  <td className="p-4 text-sm">{item.manager}</td>
                  <td className="p-4 text-sm">{item.clientId || 'N/A'}</td>
                  <td className="p-4 text-sm">{item.userId || 'N/A'}</td>
                  <td className="p-4 text-sm">{item.actualEndDate ? format(new Date(item.actualEndDate), 'dd-MMM-yy') : 'N/A'}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>      
      <div className="flex items-center justify-center">
        <TablePagination
          currentPage={currentPage}
          totalPages={data.totalPages}
          onPageChange={handlePageChange}
          maxVisiblePages={3}
          totalItems={filteredData.length}
          pageSize={pageSize}
        />
      </div>
    </div>
  );
}
