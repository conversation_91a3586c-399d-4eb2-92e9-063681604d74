'use client';

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { AlertTriangle, BarChart3 } from "lucide-react";
import { formatLargeNumber, formatOTAName } from '@/lib/format-utils';
import { cn } from "@/lib/utils";

type OTAData = {
  OTAName: string;
  TotalLooks: number;
  TotalBookings: number;
  SuccessRate: number;
  WrongRoutesRate: number;
  ThrottledRate: number;
  FailureRate: number;
  TotalFailureRate: number;
};

export default function PoorSearchesOTAsCard() {
  const [data, setData] = useState<OTAData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchWorstOTAs = async () => {
      try {
        setLoading(true);
        // Adding a timestamp parameter to avoid caching issues
        const timestamp = new Date().getTime();
        const response = await fetch(`/api/analytics-mongo/worst-otas?_=${timestamp}`);
        const result = await response.json();

        if (!result.success) {
          throw new Error(result.error || 'Failed to fetch worst performing OTAs');
        }

        setData(result.data);
      } catch (err) {
        console.error('Error fetching worst OTAs:', err);
        setError(err instanceof Error ? err.message : 'Failed to load worst performing OTAs data');
      } finally {
        setLoading(false);
      }
    };

    fetchWorstOTAs();
  }, []);  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-amber-900">Poor Search Queries OTAs</CardTitle>
            <CardDescription>Ranked by highest failure, throttling, and wrong route rates</CardDescription>
          </div>
          <BarChart3 className="h-4 w-4 text-amber-500" />
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex justify-center items-center h-48">
            <p className="text-sm text-muted-foreground">Loading...</p>
          </div>
        ) : error ? (
          <div className="flex justify-center items-center h-48">
            <p className="text-sm text-red-500">{error}</p>
          </div>
        ) : data.length === 0 ? (
          <div className="flex justify-center items-center h-48">
            <p className="text-sm text-muted-foreground">No OTA data available</p>
          </div>
        ) : (
          <div className="space-y-2 mt-2">
            {data.map((ota, index) => (
              <div key={ota.OTAName} className="pb-1.5 border-b border-dashed last:border-0 last:pb-0">
                <div className="flex justify-between items-center">
                  <div className="flex items-center space-x-2">
                    <div className={cn(
                      "font-medium truncate",
                      index === 0 && "text-amber-900",
                      index === 1 && "text-amber-800",
                      index === 2 && "text-amber-700"
                    )} title={ota.OTAName}>
                      {formatOTAName(ota.OTAName)}
                    </div>
                    {index === 0 && (
                      <div className="bg-amber-100 text-amber-800 text-xs px-1.5 py-0.5 rounded flex items-center">
                        <AlertTriangle className="h-3 w-3 mr-0.5" />
                        Worst
                      </div>
                    )}
                  </div>
                  <div className="text-sm">
                    <span className={cn(
                      "font-medium",
                      index === 0 && "text-amber-900",
                      index === 1 && "text-amber-800",
                      index === 2 && "text-amber-700"
                    )}>
                      {ota.TotalFailureRate.toFixed(1)}%
                    </span>
                    <span className="text-muted-foreground text-xs ml-1">failure</span>
                  </div>
                </div>
                <div className="flex justify-between items-center text-xs text-muted-foreground mt-0.5">
                  <div>
                    <span title="Success rate">Success: {ota.SuccessRate.toFixed(1)}%</span>
                    <span className="mx-1">•</span>
                    <span title="Total looks">{formatLargeNumber(ota.TotalLooks)} looks</span>
                  </div>
                  <div className="flex space-x-2">
                    <span title="Failure rate" className={cn(
                      index === 0 && "text-amber-600",
                      index === 1 && "text-amber-500",
                      index === 2 && "text-amber-400",
                      index > 2 && "text-red-500"
                    )}>F: {ota.FailureRate.toFixed(1)}%</span>
                    <span title="Throttled rate" className="text-amber-500">T: {ota.ThrottledRate.toFixed(1)}%</span>
                    <span title="Wrong routes rate" className="text-blue-500">W: {ota.WrongRoutesRate.toFixed(1)}%</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
